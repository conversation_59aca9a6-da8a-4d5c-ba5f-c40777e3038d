# 👁️ Schema API 作为"眼睛"：智能操作策略

## 🎯 核心理念

**Schema API 本身也是一双眼睛**，在进行操作前可以通过 Schema API 来查询页面结构，这样才能更加精准地操作。

这种"先看后做"的策略让我们的 MCP 工具具备了真正的"智能"：
- 📊 **结构感知**: 了解页面的真实结构和现状
- 🎯 **精准定位**: 基于结构分析选择最佳操作位置
- 🔍 **上下文理解**: 考虑现有内容和布局约束
- ✅ **结果验证**: 操作后再次"看"以确认结果

## 🔄 智能操作工作流

### 1. 👁️ 观察阶段 (Schema API 作为眼睛)
```typescript
// 通过 Schema API 获取页面完整结构
const analysis = await analyzePageStructure(client, pageSchemaUid);

console.log(`页面结构分析：
- 网格容器: ${analysis.hasGrid ? '✅' : '❌'}
- 行数: ${analysis.rows.length}
- 总区块数: ${getTotalBlocks(analysis)}
- 可用插入点: ${analysis.availableInsertionPoints.length}
`);
```

### 2. 🧠 分析阶段 (智能决策)
```typescript
// 基于页面结构选择最佳插入位置
const insertionPoint = selectBestInsertionPoint(analysis, 'table', {
  preferEmptySpace: true,    // 偏好空白区域
  preferNewRow: false,       // 不偏好新行
  maxColumnsPerRow: 2        // 每行最多2列
});

console.log(`选择插入位置: ${insertionPoint.description}`);
```

### 3. ⚡ 执行阶段 (精准操作)
```typescript
// 基于分析结果执行精准操作
const result = await client.insertBlockSchema(
  insertionPoint.uid,
  blockSchema,
  insertionPoint.position
);
```

### 4. ✅ 验证阶段 (再次观察)
```typescript
// 操作后再次分析，验证结果
const postAnalysis = await analyzePageStructure(client, pageSchemaUid);
const success = postAnalysis.totalBlocks > analysis.totalBlocks;
```

## 📊 页面结构分析能力

### 结构识别
```typescript
interface PageStructureAnalysis {
  pageUid: string;
  hasGrid: boolean;
  gridUid?: string;
  rows: Array<{
    uid: string;
    cols: Array<{
      uid: string;
      width?: number;
      blocks: Array<{
        uid: string;
        type: string;        // table, form, markdown, etc.
        component: string;   // TableV2, FormV2, etc.
        decorator?: string;  // TableBlockProvider, etc.
        collection?: string; // 绑定的数据集合
      }>;
    }>;
  }>;
  availableInsertionPoints: Array<{
    type: 'grid' | 'row' | 'col';
    uid: string;
    description: string;
    recommended: boolean;
  }>;
}
```

### 智能插入点选择
```typescript
// 插入点选择策略
const strategies = {
  // 1. 优先空白列（用户体验最佳）
  emptyColumns: analysis.availableInsertionPoints.filter(p => 
    p.type === 'col' && p.description.includes('0 blocks')
  ),
  
  // 2. 平衡行布局（避免单行过多列）
  balancedRows: analysis.rows.filter(row => 
    row.cols.length < maxColumnsPerRow
  ),
  
  // 3. 新行创建（当现有行已满时）
  newRowCreation: analysis.hasGrid && 
    analysis.rows.every(row => row.cols.length >= maxColumnsPerRow)
};
```

## 🎯 实际应用场景

### 场景 1: 智能添加表格区块
```typescript
const result = await smartAddBlock(client, pageUid, {
  type: 'table',
  collectionName: 'users',
  title: 'User Management',
  preferences: {
    preferEmptySpace: true,  // 优先选择空白区域
    maxColumnsPerRow: 2      // 每行最多2列
  }
});

// 输出操作过程
result.actions.forEach(action => {
  console.log(`${action.executed ? '✅' : '⏳'} ${action.description}`);
});
```

**操作过程**：
```
✅ Analyzing page structure for cafh7yoyd6w
   - Found: 1 grid, 2 rows, 3 columns, 4 blocks, 5 insertion points
✅ Selecting best insertion point for table block
   - Selected: Column (width: auto, 0 blocks) - empty space preferred
✅ Preparing table block schema
   - Schema keys: ['type', 'x-component', 'x-decorator', 'properties']
✅ Inserting block at col_xyz123
   - Insert result: { success: true, uid: 'new_block_456' }
✅ Verifying block insertion
   - Original blocks: 4, New blocks: 5, Difference: +1 ✅
```

### 场景 2: 智能页面布局优化
```typescript
const cleanupResult = await smartPageCleanup(client, pageUid, {
  removeEmptyRows: true,
  removeEmptyCols: true,
  consolidateRows: true
});

// 清理前后对比
console.log(`页面优化完成：
- 清理空行: ${emptyRowsRemoved}
- 清理空列: ${emptyColsRemoved}  
- 合并行数: ${rowsConsolidated}
`);
```

### 场景 3: 上下文感知的区块修改
```typescript
// 先分析现有区块的上下文
const analysis = await analyzePageStructure(client, pageUid);
const targetBlock = findBlockByUid(analysis, blockUid);

// 基于上下文进行智能修改
if (targetBlock.type === 'table' && targetBlock.collection === 'users') {
  // 为用户表格添加特定的操作列
  await smartModifyBlock(client, pageUid, blockUid, {
    componentProps: {
      actions: ['view', 'edit', 'delete'],
      pagination: { pageSize: 20 }
    }
  });
}
```

## 🚀 智能操作的优势

### 1. 🎯 精准性
- **结构感知**: 了解页面真实布局，避免盲目操作
- **位置优化**: 选择最佳插入位置，提升用户体验
- **冲突避免**: 检测现有内容，避免布局冲突

### 2. 🧠 智能性
- **上下文理解**: 基于现有内容做出智能决策
- **自适应布局**: 根据页面结构自动调整操作策略
- **最佳实践**: 遵循 NocoBase 的布局最佳实践

### 3. 🛡️ 可靠性
- **操作前验证**: 确保目标位置存在且可操作
- **操作后确认**: 验证操作结果，确保成功执行
- **错误恢复**: 操作失败时提供详细的错误信息

### 4. 📊 可观测性
- **详细日志**: 记录每个操作步骤和结果
- **结构分析**: 提供页面结构的详细分析报告
- **性能监控**: 跟踪操作耗时和成功率

## 🎭 与传统操作方式的对比

### 传统方式（盲目操作）
```typescript
// ❌ 传统方式：直接操作，不了解页面结构
await client.insertBlockSchema(pageUid, blockSchema, 'beforeEnd');
// 可能的问题：
// - 不知道插入到哪里
// - 可能破坏现有布局
// - 无法选择最佳位置
```

### 智能方式（先看后做）
```typescript
// ✅ 智能方式：先分析，再精准操作
const analysis = await analyzePageStructure(client, pageUid);
const bestPoint = selectBestInsertionPoint(analysis, blockType);
await client.insertBlockSchema(bestPoint.uid, blockSchema, bestPoint.position);
// 优势：
// - 了解页面完整结构
// - 选择最佳插入位置
// - 保持布局美观性
// - 提供操作可观测性
```

## 🎯 最佳实践

1. **始终先分析**: 任何操作前都要先获取页面结构
2. **智能选择位置**: 基于分析结果选择最佳操作位置
3. **验证操作结果**: 操作后再次分析以确认成功
4. **记录操作过程**: 提供详细的操作日志和结果
5. **处理边界情况**: 考虑空页面、满页面等特殊情况

这种"Schema API 作为眼睛"的智能操作策略，让我们的 MCP 工具真正具备了"看得见、想得到、做得对"的能力！
