# NocoBase MCP Block 开发方法论

## 📋 概述

本文档集记录了通过 Playwright 监测手动操作来优化 NocoBase MCP 工具的完整方法论。这个方法确保 MCP 工具生成的 Block schema 与 NocoBase 原生创建的完全一致。

## 🎯 核心方法论

### 监测驱动开发 (Monitoring-Driven Development)

```
手动操作 → API 监测 → Schema 分析 → 模板优化 → 验证测试
```

这个方法的核心思想是：**通过精确监测和复制 NocoBase 的内部机制，确保 MCP 工具的行为与原生系统完全一致**。

## 📚 文档结构

### 1. [Block 开发工作流程](./block-development-workflow.md)
**目标读者**: 开发人员、项目经理  
**阅读时间**: 15-20 分钟

**内容概要**:
- 🔄 完整的 6 阶段开发流程
- 🛠️ 环境准备和工具配置
- 📊 手动操作监测步骤
- 🔍 API 分析和 Schema 对比
- ✅ 模板优化和验证流程

**何时使用**: 开始新的 Block 类型开发时的完整指南

### 2. [API 监测技术指南](./api-monitoring-guide.md)
**目标读者**: 技术开发人员  
**阅读时间**: 25-30 分钟

**内容概要**:
- 🔧 Playwright 网络监测的详细设置
- 📋 Schema 结构分析工具和算法
- 🛠️ API 请求过滤和分析方法
- 🧪 模板生成和验证工具
- 🐛 常见问题的技术解决方案

**何时使用**: 需要深入理解技术实现细节或开发新工具时

### 3. [案例研究：Markdown 和 Table Block 修复](./case-study-markdown-table-blocks.md)
**目标读者**: 所有相关人员  
**阅读时间**: 20-25 分钟

**内容概要**:
- 🎯 实际问题的识别和分析过程
- 📊 逐步修复过程的详细记录
- 🔄 修复前后的对比分析
- 💡 关键学习点和经验总结
- 🔧 可复用的解决方案模式

**何时使用**: 学习实际案例、理解问题解决思路、寻找类似问题的解决方案

### 4. [快速参考指南](./quick-reference-guide.md)
**目标读者**: 开发人员（日常参考）  
**阅读时间**: 5-10 分钟

**内容概要**:
- 🚀 关键 API 端点和请求格式
- 📋 Schema 模式和模板代码
- 🛠️ 常用工具函数和操作
- ✅ 开发检查清单和验证步骤
- 🐛 故障排除和问题诊断

**何时使用**: 日常开发过程中的快速查询和参考

## 🎯 推荐阅读路径

### 🚀 新手入门路径
```
1. Block 开发工作流程 (了解整体方法)
   ↓
2. 快速参考指南 (掌握基础操作)
   ↓
3. 案例研究 (学习实际案例)
```

### 🔧 深入开发路径
```
1. API 监测技术指南 (掌握技术细节)
   ↓
2. Block 开发工作流程 (应用到实际开发)
   ↓
3. 快速参考指南 (日常开发参考)
```

### 🐛 问题解决路径
```
1. 快速参考指南 (查找常见问题)
   ↓
2. 案例研究 (寻找类似问题的解决方案)
   ↓
3. API 监测技术指南 (深入分析问题)
```

## 🏆 主要成果

### ✅ 已修复的 Block 类型
- **Markdown Block**: 完全修复，正确显示和功能
- **Table Block**: 完全实现，包括数据加载和交互

### 🔧 建立的基础设施
- **监测工具链**: 完整的 Playwright 监测流程
- **模板系统**: 可扩展的 Block 模板架构
- **测试框架**: 自动化验证和测试脚本
- **文档体系**: 完整的开发文档和指南

### 💡 关键发现
1. **Grid 结构的重要性**: 所有 Block 必须包装在 `Grid -> Grid.Row -> Grid.Col` 层次中
2. **组件vs装饰器**: `x-component` 和 `x-decorator` 的正确使用模式
3. **版本属性**: `version`, `x-app-version`, `_isJSONSchemaObject` 的必要性
4. **API 端点**: `uiSchemas:insertAdjacent` 是 Block 创建的核心 API

## 🚀 应用场景

### 适用的 Block 类型
- ✅ **Markdown Block** - 已验证
- ✅ **Table Block** - 已验证
- 🔄 **Form Block** - 可应用此方法
- 🔄 **Details Block** - 可应用此方法
- 🔄 **List Block** - 可应用此方法
- 🔄 **Grid Card Block** - 可应用此方法
- 🔄 **Calendar Block** - 可应用此方法
- 🔄 **Chart Block** - 可应用此方法

### 扩展可能性
- **自定义 Block 类型**: 为特定业务需求开发新的 Block
- **复合 Block**: 组合多个 Block 创建复杂布局
- **动态 Block**: 根据数据动态生成 Block 结构
- **模板市场**: 创建可共享的 Block 模板库

## 🛠️ 技术栈

### 核心工具
- **Playwright**: 浏览器自动化和网络监测
- **NocoBase API**: 直接 API 调用和 schema 操作
- **TypeScript**: 类型安全的模板开发
- **MCP Protocol**: 与 AI 助手的集成

### 开发环境
- **测试环境**: `https://app.dev.orb.local`
- **应用**: `mcp_playground`
- **认证**: Token-based authentication

## 🤝 贡献指南

### 如何贡献
1. **阅读文档**: 从工作流程指南开始了解方法论
2. **环境设置**: 按照快速参考指南配置开发环境
3. **选择任务**: 从未实现的 Block 类型中选择
4. **遵循流程**: 使用监测驱动开发方法
5. **更新文档**: 记录新的发现和解决方案

### 文档维护
- 保持文档的时效性和准确性
- 添加新的案例研究和最佳实践
- 更新工具和方法的改进
- 收集和整理用户反馈

## 📞 支持和反馈

### 问题报告
- 使用 GitHub Issues 报告问题
- 提供详细的重现步骤
- 包含相关的日志和错误信息

### 改进建议
- 提出新的方法论改进
- 分享成功的应用案例
- 建议新的工具和技术

---

这个方法论代表了我们在 NocoBase MCP 集成方面的重要突破，建立了一个可持续、可扩展和可复用的开发方法。通过系统性的监测和分析，我们不仅解决了当前的问题，还为未来的开发奠定了坚实的基础。
