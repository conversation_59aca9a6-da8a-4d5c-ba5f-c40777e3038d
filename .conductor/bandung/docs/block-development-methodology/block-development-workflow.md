# NocoBase MCP Block 开发工作流程

## 📋 概述

本文档描述了通过 Playwright 监测手动操作来优化 NocoBase MCP 工具的完整工作流程。这个方法确保 MCP 工具生成的 schema 与 NocoBase 原生创建的完全一致。

## 🔄 工作流程

### 阶段 1：环境准备

#### 1.1 启动测试环境
```bash
# 启动 MCP 服务器连接到测试环境
npm run start -- --base-url "https://app.dev.orb.local/api" \
  --token "YOUR_TOKEN" \
  --app "mcp_playground"
```

#### 1.2 准备 Playwright 环境
```bash
# 安装 Playwright（如果需要）
npm install playwright

# 启动浏览器并导航到测试页面
# 使用 browser_navigate_Playwright 工具
```

### 阶段 2：手动操作监测

#### 2.1 开始网络监测
```javascript
// 使用 browser_network_requests_Playwright 开始监测
// 清空之前的网络请求记录
```

#### 2.2 执行手动操作
1. **打开 Add Block 菜单**
   ```javascript
   // 点击 "Add block" 按钮
   browser_click_Playwright({ element: "Add block 按钮", ref: "..." })
   ```

2. **选择 Block 类型**
   ```javascript
   // 例如：点击 "Table" 或 "Markdown"
   browser_click_Playwright({ element: "Table 菜单项", ref: "..." })
   ```

3. **配置 Block 参数**
   ```javascript
   // 对于 Table：选择 Collection
   browser_click_Playwright({ element: "Users 菜单项", ref: "..." })
   
   // 对于 Markdown：直接创建
   // 系统会自动创建 block
   ```

#### 2.3 捕获 API 请求
```javascript
// 获取所有网络请求
const requests = await browser_network_requests_Playwright();

// 关键 API 请求模式：
// POST /api/uiSchemas:insertAdjacent/{parentUid}?position=beforeEnd
// GET /api/{collection}:list (对于数据 blocks)
```

### 阶段 3：API 分析

#### 3.1 识别关键 API 调用
```javascript
// 示例：Markdown Block 创建
POST /api/uiSchemas:insertAdjacent/szdigp6b1ug?position=beforeEnd
Content-Type: application/json

{
  "type": "void",
  "version": "2.0",
  "x-component": "Markdown.Void",
  "x-decorator": "CardItem",
  // ... 完整 schema
}
```

#### 3.2 提取 Schema 结构
```javascript
// 使用 check-tab-schema.js 获取创建后的完整 schema
const schema = await client.getUISchema(parentUid);

// 分析 schema 结构，找到新创建的 block
const findNewBlock = (obj, targetType) => {
  // 递归查找新创建的 block
};
```

### 阶段 4：模板优化

#### 4.1 对比分析
```typescript
// 对比手动创建的 schema 与当前模板
// 手动创建的结构：
{
  "type": "void",
  "version": "2.0",
  "x-decorator": "CardItem",
  "x-component": "Markdown.Void",
  "x-app-version": "1.8.14",
  "_isJSONSchemaObject": true,
  // ...
}

// 当前模板结构：
{
  "type": "void",
  "x-component": "CardItem",  // ❌ 错误
  "properties": {
    "innerUid": {
      "x-component": "Markdown.Void"  // ❌ 嵌套错误
    }
  }
}
```

#### 4.2 识别关键差异
1. **组件层次结构**
   - ❌ 错误：`x-component: "CardItem"` + 嵌套结构
   - ✅ 正确：`x-decorator: "CardItem"` + 直接结构

2. **Grid 层次结构**
   - ❌ 错误：直接在 Grid 下添加 Block
   - ✅ 正确：Grid -> Grid.Row -> Grid.Col -> Block

3. **必需属性**
   - ✅ 添加：`version: "2.0"`
   - ✅ 添加：`x-app-version: "1.8.14"`
   - ✅ 添加：`_isJSONSchemaObject: true`

### 阶段 5：模板修复

#### 5.1 更新 Block 模板
```typescript
// src/block-templates.ts
export const createMarkdownBlockSchema = (options) => {
  const rowUid = generateUid();
  const colUid = generateUid();
  const blockUid = generateUid();

  return {
    type: 'void',
    version: '2.0',
    'x-uid': rowUid,
    'x-component': 'Grid.Row',
    'x-app-version': '1.8.14',
    '_isJSONSchemaObject': true,
    properties: {
      [colUid]: {
        type: 'void',
        version: '2.0',
        'x-component': 'Grid.Col',
        properties: {
          [blockUid]: {
            type: 'void',
            version: '2.0',
            'x-decorator': 'CardItem',  // ✅ 正确
            'x-component': 'Markdown.Void',  // ✅ 正确
            // ... 其他属性
          }
        }
      }
    }
  };
};
```

#### 5.2 重新构建项目
```bash
npm run build
```

### 阶段 6：验证测试

#### 6.1 创建测试脚本
```javascript
// test-fixed-block.js
import { NocoBaseClient } from './dist/client.js';
import { handleAddMarkdownBlock } from './dist/tools/blocks.js';

const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'YOUR_TOKEN',
  app: 'mcp_playground'
});

async function main() {
  const result = await handleAddMarkdownBlock(client, {
    parentUid: 'szdigp6b1ug',
    content: '# Test Content',
    position: 'beforeEnd'
  });
  
  console.log('✅ Block 创建成功:', result);
}
```

#### 6.2 浏览器验证
```javascript
// 刷新页面查看结果
browser_navigate_Playwright({
  url: "https://app.dev.orb.local/apps/mcp_playground/admin/jf8515glj1b?_t=new"
});

// 等待加载并检查页面内容
browser_wait_for_Playwright({ time: 8 });
browser_snapshot_Playwright();
```

## 🎯 关键成功因素

### 1. 精确的 API 监测
- 使用 `browser_network_requests_Playwright` 捕获所有请求
- 重点关注 `uiSchemas:insertAdjacent` API
- 记录完整的请求体和响应

### 2. 完整的 Schema 分析
- 获取创建前后的完整 schema
- 递归分析 schema 结构差异
- 识别所有必需的属性和层次结构

### 3. 系统性的模板修复
- 逐一对比每个属性
- 确保 Grid 层次结构正确
- 添加所有版本和元数据属性

### 4. 全面的验证测试
- 创建专门的测试脚本
- 在浏览器中验证显示效果
- 确保功能完全一致

## 📊 适用的 Block 类型

这个工作流程适用于所有 NocoBase Block 类型：

- ✅ **Markdown Block** - 已验证
- ✅ **Table Block** - 已验证
- 🔄 **Form Block** - 可应用
- 🔄 **Details Block** - 可应用
- 🔄 **List Block** - 可应用
- 🔄 **Grid Card Block** - 可应用
- 🔄 **Calendar Block** - 可应用
- 🔄 **Chart Block** - 可应用

## 🛠️ 工具和脚本

### 必需工具
- `browser_navigate_Playwright` - 页面导航
- `browser_click_Playwright` - 元素点击
- `browser_network_requests_Playwright` - 网络监测
- `browser_snapshot_Playwright` - 页面快照

### 辅助脚本
- `check-tab-schema.js` - Schema 检查
- `test-{block-type}.js` - Block 测试
- `call-mcp-tools.js` - MCP 工具调用

## 📝 最佳实践

1. **始终先监测手动操作**：确保了解正确的 API 调用模式
2. **完整记录 Schema 结构**：包括所有属性和层次关系
3. **逐步修复模板**：一次修复一个 Block 类型
4. **全面验证结果**：确保功能和显示完全一致
5. **文档化过程**：记录每个修复的详细过程

这个工作流程确保了 MCP 工具生成的 Block 与 NocoBase 原生创建的完全一致，提供了可靠和可重复的开发方法。
