# NocoBase List Block Operations - Usage Examples

## Quick Start

The MCP NocoBase server now includes comprehensive List Block operations. Here are practical examples of how to use the new tools.

## Basic List Operations

### 1. Get List Data with Pagination and Filtering

```bash
# Get users list with pagination and filters
mcp-client call get_list_data '{
  "collectionName": "users",
  "page": 1,
  "pageSize": 10,
  "filter": {
    "status": "active",
    "role": "admin"
  },
  "fields": ["id", "name", "email", "createdAt"],
  "appends": ["profile", "roles"],
  "sort": ["name", "-createdAt"]
}'
```

### 2. Create New List Item

```bash
# Create a new user
mcp-client call create_list_item '{
  "collectionName": "users",
  "values": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "status": "active",
    "role": "user"
  },
  "updateAssociationValues": true
}'
```

### 3. Update List Item

```bash
# Update user information
mcp-client call update_list_item '{
  "collectionName": "users",
  "itemId": 1,
  "values": {
    "name": "<PERSON>",
    "status": "inactive"
  },
  "updateAssociationValues": true
}'
```

### 4. Delete Single Item

```bash
# Delete a user
mcp-client call delete_list_item '{
  "collectionName": "users",
  "itemId": 1
}'
```

### 5. Bulk Delete Items

```bash
# Delete multiple users
mcp-client call bulk_delete_list_items '{
  "collectionName": "users",
  "itemIds": [1, 2, 3, 4, 5]
}'
```

## List Configuration

### 6. Add Action Button to List

```bash
# Add a "Create New" button
mcp-client call add_list_action '{
  "listUid": "list_users_123",
  "actionType": "create",
  "position": "beforeEnd"
}'

# Add a custom action button
mcp-client call add_list_action '{
  "listUid": "list_users_123",
  "actionType": "custom",
  "customConfig": {
    "title": "Send Email",
    "action": "sendEmail",
    "icon": "MailOutlined",
    "type": "default",
    "align": "right"
  }
}'
```

### 7. Configure List Fields Display

```bash
# Configure which fields to show in the list
mcp-client call configure_list_fields '{
  "listUid": "list_users_123",
  "fields": [
    {
      "name": "name",
      "title": "Full Name",
      "component": "Input.ReadPretty",
      "span": 8
    },
    {
      "name": "email",
      "title": "Email Address",
      "component": "Input.ReadPretty",
      "span": 8
    },
    {
      "name": "status",
      "title": "Status",
      "component": "Select.ReadPretty",
      "span": 8
    }
  ]
}'
```

### 8. Configure List Item Actions

```bash
# Configure actions for each list item
mcp-client call configure_list_item_actions '{
  "listUid": "list_users_123",
  "actions": [
    {
      "actionType": "view"
    },
    {
      "actionType": "edit"
    },
    {
      "actionType": "delete"
    },
    {
      "actionType": "custom",
      "customConfig": {
        "title": "Send Message",
        "action": "sendMessage",
        "icon": "MessageOutlined",
        "component": "Action.Link"
      }
    }
  ]
}'
```

## Advanced Operations

### 9. Export List Data

```bash
# Export users to Excel
mcp-client call export_list_data '{
  "collectionName": "users",
  "format": "xlsx",
  "fields": ["id", "name", "email", "status", "createdAt"],
  "filter": {
    "status": "active"
  }
}'

# Export to CSV with pagination
mcp-client call export_list_data '{
  "collectionName": "users",
  "format": "csv",
  "page": 1,
  "pageSize": 100
}'
```

### 10. Import List Data

```bash
# Import users from Excel file
mcp-client call import_list_data '{
  "collectionName": "users",
  "file": "/path/to/users.xlsx",
  "fields": ["name", "email", "status"],
  "updateStrategy": "upsert"
}'
```

### 11. Filter List

```bash
# Apply filters to a list
mcp-client call filter_list '{
  "listUid": "list_users_123",
  "filter": {
    "status": "active",
    "createdAt": {
      "$gte": "2024-01-01"
    }
  },
  "page": 1,
  "pageSize": 20
}'
```

### 12. Refresh List

```bash
# Refresh list data
mcp-client call refresh_list '{
  "listUid": "list_users_123"
}'
```

### 13. Custom List Request

```bash
# Send custom API request
mcp-client call custom_list_request '{
  "url": "/api/users/custom-action",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "data": {
    "action": "bulk_update_status",
    "userIds": [1, 2, 3],
    "newStatus": "verified"
  }
}'
```

## Real-World Scenarios

### Scenario 1: User Management Dashboard

```bash
# 1. Get active users
mcp-client call get_list_data '{
  "collectionName": "users",
  "filter": {"status": "active"},
  "fields": ["id", "name", "email", "lastLogin"],
  "sort": ["-lastLogin"]
}'

# 2. Configure the list to show relevant fields
mcp-client call configure_list_fields '{
  "listUid": "users_dashboard_list",
  "fields": [
    {"name": "name", "title": "Name", "span": 6},
    {"name": "email", "title": "Email", "span": 6},
    {"name": "lastLogin", "title": "Last Login", "span": 6},
    {"name": "status", "title": "Status", "span": 6}
  ]
}'

# 3. Add management actions
mcp-client call configure_list_item_actions '{
  "listUid": "users_dashboard_list",
  "actions": [
    {"actionType": "view"},
    {"actionType": "edit"},
    {"actionType": "custom", "customConfig": {
      "title": "Reset Password",
      "action": "resetPassword",
      "icon": "KeyOutlined"
    }}
  ]
}'
```

### Scenario 2: Product Catalog Management

```bash
# 1. Get products with inventory info
mcp-client call get_list_data '{
  "collectionName": "products",
  "fields": ["id", "name", "price", "stock", "category"],
  "appends": ["category", "images"],
  "filter": {"stock": {"$gt": 0}},
  "sort": ["category", "name"]
}'

# 2. Add bulk operations
mcp-client call add_list_action '{
  "listUid": "products_list",
  "actionType": "custom",
  "customConfig": {
    "title": "Bulk Update Prices",
    "action": "bulkUpdatePrices",
    "icon": "DollarOutlined"
  }
}'

# 3. Export product catalog
mcp-client call export_list_data '{
  "collectionName": "products",
  "format": "xlsx",
  "fields": ["name", "price", "stock", "category.name"],
  "filter": {"status": "published"}
}'
```

## Error Handling

All tools return structured responses. Success responses include the operation result:

```json
{
  "content": [
    {
      "type": "text",
      "text": "List data retrieved successfully:\n{...}"
    }
  ]
}
```

Error responses include detailed error information:

```json
{
  "content": [
    {
      "type": "text",
      "text": "Error retrieving list data: Collection 'invalid_collection' not found"
    }
  ]
}
```

## Tips and Best Practices

1. **Pagination**: Always use pagination for large datasets to improve performance
2. **Field Selection**: Specify only needed fields to reduce response size
3. **Filtering**: Use filters to reduce data transfer and improve user experience
4. **Associations**: Use `appends` to include related data efficiently
5. **Sorting**: Provide meaningful default sorting for better UX
6. **Error Handling**: Always check response format and handle errors appropriately

## Integration with Workflows

Many operations support workflow triggers:

```bash
# Create item with workflow trigger
mcp-client call create_list_item '{
  "collectionName": "orders",
  "values": {"status": "pending", "amount": 100},
  "triggerWorkflows": "order_created,send_notification"
}'
```

This comprehensive set of List Block operations provides everything needed to build sophisticated list-based interfaces in NocoBase applications.
