# NocoBase 菜单删除和移动功能指南

## 概述

我们已经成功为 mcp-server-nocobase 添加了菜单删除和移动功能。这些功能允许你通过 MCP 工具来管理 NocoBase 的菜单结构。

## 新增功能

### 1. 菜单删除功能 (`delete_route`)

**功能描述：** 删除指定的路由和菜单项，包括其对应的 UI Schema。

**参数：**
- `id`: 要删除的路由ID（字符串或数字）

**示例：**
```json
{
  "name": "delete_route",
  "arguments": {
    "id": 24
  }
}
```

**功能特点：**
- 自动删除路由记录
- 自动删除对应的页面 Schema
- 自动删除对应的菜单 Schema
- 子路由（如 tabs）会被自动删除

### 2. 菜单移动功能 (`move_route`)

**功能描述：** 将菜单项移动到不同的位置或父级分组中。

**参数：**
- `sourceId`: 要移动的路由ID（必需）
- `targetId`: 目标路由ID（可选，根据position而定）
- `position`: 移动位置，支持以下选项：
  - `"before"`: 插入到目标项前面
  - `"after"`: 插入到目标项后面
  - `"inside"`: 移动到目标分组内部
  - `"first"`: 移动到指定父级的第一个位置
  - `"last"`: 移动到指定父级的最后一个位置
- `newParentId`: 新的父级ID（可选，用于更改父级）

**示例：**

1. 将页面移动到分组内部：
```json
{
  "name": "move_route",
  "arguments": {
    "sourceId": 20,
    "targetId": 3,
    "position": "inside"
  }
}
```

2. 将分组移动到另一个项目后面：
```json
{
  "name": "move_route",
  "arguments": {
    "sourceId": 23,
    "targetId": 1,
    "position": "after"
  }
}
```

3. 将项目移动到指定父级的第一个位置：
```json
{
  "name": "move_route",
  "arguments": {
    "sourceId": 20,
    "newParentId": 3,
    "position": "first"
  }
}
```

## 测试结果

### 删除功能测试
✅ **成功删除 group2_group1_page1 (ID: 24)**
- 路由记录已删除
- 页面 Schema 已删除
- 菜单 Schema 已删除
- 子路由（tabs）自动删除

### 移动功能测试
✅ **成功将 page_2 移动到 group_1 内部**
- 路由的 parentId 已更新
- 菜单结构已调整
- 子路由跟随移动

✅ **成功将 group2_group1 移动到根级别**
- 从 group_2 移出到根级别
- 排序位置正确调整

## 技术实现

### 删除功能实现
1. 获取路由信息
2. 删除路由记录
3. 删除对应的 UI Schema（页面和菜单）
4. 错误处理和回滚

### 移动功能实现
1. 解析移动参数和位置
2. 更新路由的 parentId（如需要）
3. 调用排序 API 调整位置
4. 移动菜单 Schema（如存在）
5. 错误处理和警告

## API 调用示例

### 使用 MCP 工具
```bash
echo '{"jsonrpc":"2.0","id":1,"method":"tools/call","params":{"name":"delete_route","arguments":{"id":24}}}' | \
node dist/index.js --base-url http://103.121.94.113:13000/api \
--token YOUR_TOKEN --app mcp_playground
```

### 直接 API 调用
```bash
# 删除路由
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
-H "X-App: mcp_playground" \
"http://103.121.94.113:13000/api/desktopRoutes:destroy?filterByTk=24"

# 移动路由
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
-H "X-App: mcp_playground" -H "Content-Type: application/json" \
-d '{"sourceId":20,"targetId":3,"method":"insertAfter"}' \
"http://103.121.94.113:13000/api/desktopRoutes:move"

# 更新父级
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
-H "X-App: mcp_playground" -H "Content-Type: application/json" \
-d '{"parentId":3}' \
"http://103.121.94.113:13000/api/desktopRoutes:update?filterByTk=20"
```

## 注意事项

1. **权限要求：** 需要管理员权限才能执行删除和移动操作
2. **数据完整性：** 删除操作会同时清理相关的 Schema 数据
3. **菜单同步：** 移动操作会尝试同步菜单 Schema，但如果失败不会阻止路由移动
4. **错误处理：** 所有操作都包含适当的错误处理和回滚机制

## 下一步改进

1. 添加批量操作支持
2. 改进菜单 Schema 移动的可靠性
3. 添加操作历史记录
4. 支持更复杂的移动规则
