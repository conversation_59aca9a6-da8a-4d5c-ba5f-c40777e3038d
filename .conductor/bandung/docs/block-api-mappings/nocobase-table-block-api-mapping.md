# 通过 API 创建完整表格区块的完整流程

## 1. 基础表格结构创建

首先使用 `createTableBlockUISchema` 函数创建基础表格结构：

```javascript
import { createTableBlockUISchema } from '@nocobase/client';

const tableSchema = createTableBlockUISchema({
  dataSource: 'your_data_source',
  collectionName: 'your_collection',
  association: 'optional_association'
});
```

这会创建一个包含以下结构的表格：
- TableBlockProvider 装饰器
- 基础配置（分页、索引列等）
- 操作栏（ActionBar）用于添加按钮
- 表格主体（TableV2）
- 操作列（Action Column）

## 2. 添加表格操作按钮

通过 `table:configureActions` 初始化器添加操作按钮：

### 常用操作按钮：
1. **添加（CreateActionInitializer）**
   - 添加新记录按钮
   - 包含弹窗和表单

2. **筛选（FilterActionInitializer）**
   - 筛选按钮
   - 打开筛选表单

3. **刷新（RefreshActionInitializer）**
   - 刷新表格数据

4. **删除（BulkDestroyActionInitializer）**
   - 批量删除选中记录

5. **关联（AssociateActionInitializer）**
   - 关联其他记录

6. **链接（LinkActionInitializer）**
   - 自定义链接操作

### 添加方式示例：
```javascript
// 在 actions 属性下添加按钮
tableSchema.properties.actions.properties.addButton = {
  title: '{{ t("Add new") }}',
  'x-action': 'create',
  'x-component': 'Action',
  'x-component-props': {
    type: 'primary',
    icon: 'PlusOutlined'
  }
  // ... 其他配置
};
```

## 3. 添加表格字段列

通过 `table:configureColumns` 初始化器添加字段列：

### 字段列类型：
1. **普通字段列**
   - 文本、数字、日期等基础字段
   - 使用 `useTableColumnInitializerFields` 获取可用字段

2. **关联字段列**
   - 关联表的字段
   - 使用 `useAssociatedTableColumnInitializerFields` 获取

3. **继承字段列**
   - 父表的字段
   - 使用 `useInheritsTableColumnInitializerFields` 获取

### 添加方式示例：
```javascript
// 在表格主体下添加字段列
const columnKey = uid(); // 生成唯一ID
tableSchema.properties[columnKey].properties.fieldName = {
  type: 'void',
  'x-decorator': 'TableV2.Column.Decorator',
  'x-component': 'TableV2.Column',
  properties: {
    fieldName: {
      'x-collection-field': 'collection.fieldName',
      'x-component': 'CollectionField',
      'x-read-pretty': true
    }
  }
};
```

## 4. 添加行操作按钮

通过 `table:configureItemActions` 初始化器在操作列中添加行级操作按钮：

### 常用行操作：
1. **查看（ViewActionInitializer）**
   - 查看记录详情

2. **编辑（UpdateActionInitializer）**
   - 编辑记录

3. **删除（DestroyActionInitializer）**
   - 删除单条记录

4. **取消关联（DisassociateActionInitializer）**
   - 取消关联关系

5. **添加子项（CreateChildInitializer）**
   - 树形表格添加子节点

### 添加方式示例：
```javascript
// 在操作列的 Space 组件中添加按钮
const actionColumn = tableSchema.properties[columnKey].properties.actions;
const spaceComponent = actionColumn.properties[uid()];
spaceComponent.properties.viewButton = {
  title: '{{ t("View") }}',
  'x-action': 'view',
  'x-component': 'Action.Link'
  // ... 其他配置
};
```

## 5. 设置表格属性

通过 `blockSettings:table` 配置表格属性：

### 可配置属性：
1. **标题（SchemaSettingsBlockTitleItem）**
   - 设置区块标题

2. **高度（SchemaSettingsBlockHeightItem）**
   - 设置表格高度

3. **树形表格（treeTable）**
   - 启用树形表格显示

4. **拖拽排序（enableDragAndDropSorting）**
   - 启用拖拽排序功能

5. **索引列（enableIndexColumn）**
   - 显示行索引列

6. **数据范围（setTheDataScopeSchemaSettingsItem）**
   - 设置数据过滤条件

7. **默认排序（setDefaultSortingRulesSchemaSettingsItem）**
   - 设置默认排序规则

8. **分页大小（RecordsPerPage）**
   - 设置每页显示记录数

9. **表格尺寸（tableSize）**
   - 设置表格大小（大、中、小）

## 6. 设置字段列属性

通过 `fieldSettings:TableColumn` 配置字段列属性：

### 可配置属性：
1. **列标题（customColumnTitle）**
   - 自定义列标题

2. **工具提示（editTooltip）**
   - 设置列工具提示

3. **列宽度（columnWidth）**
   - 设置列宽度

4. **可排序（sortable）**
   - 启用列排序

5. **默认值（setDefaultValue）**
   - 设置字段默认值

6. **必填（required）**
   - 设置字段必填

7. **模式（pattern）**
   - 设置字段模式（可编辑、只读、易读）

8. **固定列（fixed）**
   - 设置列固定位置

9. **隐藏列（hidden）**
   - 隐藏列显示

10. **字段组件（fieldComponentSettingsItem）**
    - 设置字段显示组件

## 完整示例代码：

```javascript
import { createTableBlockUISchema, uid } from '@nocobase/client';

// 1. 创建基础表格结构
const tableSchema = createTableBlockUISchema({
  dataSource: 'main',
  collectionName: 'users'
});

// 2. 添加操作按钮
tableSchema.properties.actions.properties.createButton = {
  title: '{{ t("Add new") }}',
  'x-action': 'create',
  'x-component': 'Action',
  'x-component-props': {
    type: 'primary',
    icon: 'PlusOutlined'
  }
};

tableSchema.properties.actions.properties.refreshButton = {
  title: '{{ t("Refresh") }}',
  'x-action': 'refresh',
  'x-component': 'Action',
  'x-component-props': {
    icon: 'ReloadOutlined'
  }
};

// 3. 添加字段列
const tableKey = Object.keys(tableSchema.properties).find(key => 
  tableSchema.properties[key]['x-component'] === 'TableV2'
);

tableSchema.properties[tableKey].properties.nameColumn = {
  type: 'void',
  'x-decorator': 'TableV2.Column.Decorator',
  'x-component': 'TableV2.Column',
  properties: {
    name: {
      'x-collection-field': 'users.name',
      'x-component': 'CollectionField',
      'x-read-pretty': true
    }
  }
};

// 4. 添加行操作按钮
const actionsColumn = tableSchema.properties[tableKey].properties.actions;
const spaceKey = Object.keys(actionsColumn.properties).find(key => 
  actionsColumn.properties[key]['x-component'] === 'Space'
);

actionsColumn.properties[spaceKey].properties.viewButton = {
  title: '{{ t("View") }}',
  'x-action': 'view',
  'x-component': 'Action.Link'
};

actionsColumn.properties[spaceKey].properties.editButton = {
  title: '{{ t("Edit") }}',
  'x-action': 'update',
  'x-component': 'Action.Link'
};

// 5. 设置表格属性（通过装饰器属性）
tableSchema['x-decorator-props'].showIndex = true;
tableSchema['x-decorator-props'].params.pageSize = 10;

// 6. 设置列属性
tableSchema.properties[tableKey].properties.nameColumn['x-component-props'] = {
  width: 200
};
```

这个完整的流程涵盖了通过 API 创建一个功能完整的表格区块所需的所有步骤，包括基础结构、操作按钮、字段列、行操作以及各种配置选项。