# NocoBase Grid Card Block 操作与 API 调用对应关系分析报告

## 概述

本报告深入分析 NocoBase Grid Card Block（网格卡片块）的各种操作如何通过内置 API 进行调用，重点关注操作配置、API 映射关系和实际应用场景。

## 1. Grid Card Block 架构概览

### 1.1 核心组件结构

```
GridCard.Decorator (块装饰器)
├── ActionBar (操作栏)
│   ├── Filter (筛选)
│   ├── Add New (新增)
│   ├── Refresh (刷新)
│   ├── Import (导入)
│   ├── Export (导出)
│   └── Custom Request (自定义请求)
└── GridCard (卡片容器)
    └── GridCard.Item (单个卡片)
        ├── Grid (网格布局)
        └── ActionBar (卡片操作栏)
            ├── View (查看)
            ├── Edit (编辑)
            ├── Delete (删除)
            ├── Popup (弹窗)
            ├── Update Record (更新记录)
            ├── Custom Request (自定义请求)
            └── Link (链接)
```

### 1.2 主要文件位置

- **块初始化器**: `/packages/core/client/src/modules/blocks/data-blocks/grid-card/GridCardBlockInitializer.tsx`
- **操作初始化器**: `/packages/core/client/src/modules/blocks/data-blocks/grid-card/GridCardActionInitializers.tsx`
- **卡片操作初始化器**: `/packages/core/client/src/modules/blocks/data-blocks/grid-card/gridCardItemActionInitializers.tsx`
- **UI Schema 创建**: `/packages/core/client/src/modules/blocks/data-blocks/grid-card/createGridCardBlockUISchema.ts`

## 2. 块级别操作与 API 映射

### 2.1 筛选操作 (Filter)

**操作配置**:
```typescript
{
  name: 'filter',
  title: "{{t('Filter')}}",
  Component: 'FilterActionInitializer',
  schema: {
    'x-align': 'left',
  },
}
```

**API 调用**:
- **端点**: `GET /{collection}:list`
- **参数**: 通过 `filter` 参数传递筛选条件
- **权限**: 无特殊权限要求

**实际应用**:
```typescript
// 筛选操作调用示例
const applyFilter = async (collectionName, filterParams) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:list`,
    method: 'GET',
    params: {
      filter: filterParams,
      pageSize: 12, // Grid Card 默认分页大小
    }
  });
};
```

### 2.2 新增操作 (Add New)

**操作配置**:
```typescript
{
  name: 'addNew',
  title: "{{t('Add new')}}",
  Component: 'CreateActionInitializer',
  schema: {
    'x-align': 'right',
    'x-decorator': 'ACLActionProvider',
    'x-acl-action-props': {
      skipScopeCheck: true,
    },
  },
  useVisible: () => useActionAvailable('create'),
}
```

**API 调用**:
- **端点**: `POST /{collection}:create`
- **权限**: `create`
- **ACL 控制**: 是

**实际应用**:
```typescript
// 新增记录调用示例
const createRecord = async (collectionName, recordData) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:create`,
    method: 'POST',
    data: {
      values: recordData,
      whitelist: ['field1', 'field2'], // 可选：允许字段
      blacklist: ['id'], // 可选：禁止字段
      updateAssociationValues: true, // 可选：更新关联值
    }
  });
};
```

### 2.3 刷新操作 (Refresh)

**操作配置**:
```typescript
{
  name: 'refresh',
  title: "{{t('Refresh')}}",
  Component: 'RefreshActionInitializer',
  schema: {
    'x-align': 'right',
  },
}
```

**API 调用**:
- **端点**: `GET /{collection}:list`
- **作用**: 重新加载数据
- **参数**: 保持当前筛选和分页状态

**实际应用**:
```typescript
// 刷新数据调用示例
const refreshData = async (collectionName, currentParams) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:list`,
    method: 'GET',
    params: {
      ...currentParams,
      pageSize: 12,
    }
  });
};
```

### 2.4 导入操作 (Import)

**操作配置**:
```typescript
{
  name: 'import',
  title: "{{t('Import')}}",
  Component: 'ImportActionInitializer',
  schema: {
    'x-align': 'right',
    'x-acl-action': 'importXlsx',
    'x-decorator': 'ACLActionProvider',
    'x-acl-action-props': {
      skipScopeCheck: true,
    },
  },
  useVisible: () => useActionAvailable('import'),
}
```

**API 调用**:
- **端点**: `POST /{collection}:importXlsx`
- **权限**: `importXlsx`
- **数据类型**: 文件上传

**实际应用**:
```typescript
// 导入数据调用示例
const importData = async (collectionName, file) => {
  const apiClient = useAPIClient();
  const formData = new FormData();
  formData.append('file', file);
  
  return await apiClient.request({
    url: `/${collectionName}:importXlsx`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  });
};
```

### 2.5 导出操作 (Export)

**操作配置**:
```typescript
{
  name: 'export',
  title: "{{t('Export')}}",
  Component: 'ExportActionInitializer',
  useVisible: () => useActionAvailable('export'),
  schema: {
    'x-align': 'right',
    'x-decorator': 'ACLActionProvider',
    'x-acl-action-props': {
      skipScopeCheck: true,
    },
  },
}
```

**API 调用**:
- **端点**: `POST /{collection}:exportXlsx`
- **权限**: `export`
- **响应**: 文件下载

**实际应用**:
```typescript
// 导出数据调用示例
const exportData = async (collectionName, filterParams) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:exportXlsx`,
    method: 'POST',
    data: {
      filter: filterParams,
    },
    responseType: 'blob', // 文件下载
  });
};
```

### 2.6 自定义请求 (Custom Request)

**操作配置**:
```typescript
{
  name: 'customRequest',
  title: '{{t("Custom request")}}',
  Component: 'CustomRequestInitializer',
  schema: {
    'x-action': 'customize:table:request:global',
  },
}
```

**API 调用**:
- **端点**: `POST /customRequests:send/{requestId}`
- **权限**: 自定义
- **灵活性**: 高

**实际应用**:
```typescript
// 自定义请求调用示例
const sendCustomRequest = async (requestId, data) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/customRequests:send/${requestId}`,
    method: 'POST',
    data: {
      currentRecord: data,
      $nForm: formData,
      $nSelectedRecord: selectedRecord,
    }
  });
};
```

## 3. 卡片级别操作与 API 映射

### 3.1 查看操作 (View)

**操作配置**:
```typescript
{
  name: 'view',
  title: '{{t("View")}}',
  Component: 'ViewActionInitializer',
  schema: {
    'x-component': 'Action.Link',
    'x-action': 'view',
    'x-decorator': 'ACLActionProvider',
    'x-align': 'left',
  },
  useVisible: () => useActionAvailable('get'),
}
```

**API 调用**:
- **端点**: `GET /{collection}:get/{id}`
- **权限**: `get`
- **参数**: `filterByTk` (主键值)

**实际应用**:
```typescript
// 查看记录调用示例
const viewRecord = async (collectionName, recordId) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:get`,
    method: 'GET',
    params: {
      filterByTk: recordId,
      fields: ['id', 'name', 'description'], // 可选：返回字段
      appends: ['relationField'], // 可选：关联字段
    }
  });
};
```

### 3.2 编辑操作 (Edit)

**操作配置**:
```typescript
{
  name: 'edit',
  title: '{{t("Edit")}}',
  Component: 'UpdateActionInitializer',
  schema: {
    'x-component': 'Action.Link',
    'x-action': 'update',
    'x-decorator': 'ACLActionProvider',
    'x-align': 'left',
  },
  useVisible: () => useActionAvailable('update'),
}
```

**API 调用**:
- **端点**: `PUT /{collection}:update/{id}`
- **权限**: `update`
- **参数**: `filterByTk`, `values`

**实际应用**:
```typescript
// 编辑记录调用示例
const updateRecord = async (collectionName, recordId, updateData) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:update`,
    method: 'PUT',
    data: {
      filterByTk: recordId,
      values: updateData,
      whitelist: ['name', 'description'], // 可选：允许字段
      blacklist: ['id'], // 可选：禁止字段
      updateAssociationValues: true, // 可选：更新关联值
    }
  });
};
```

### 3.3 删除操作 (Delete)

**操作配置**:
```typescript
{
  name: 'delete',
  title: '{{t("Delete")}}',
  Component: 'DestroyActionInitializer',
  schema: {
    'x-component': 'Action.Link',
    'x-action': 'destroy',
    'x-decorator': 'ACLActionProvider',
    'x-align': 'left',
  },
  useVisible: () => useActionAvailable('destroy'),
}
```

**API 调用**:
- **端点**: `DELETE /{collection}:destroy/{id}`
- **权限**: `destroy`
- **参数**: `filterByTk`

**实际应用**:
```typescript
// 删除记录调用示例
const deleteRecord = async (collectionName, recordId) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:destroy`,
    method: 'DELETE',
    data: {
      filterByTk: recordId,
    }
  });
};
```

### 3.4 弹窗操作 (Popup)

**操作配置**:
```typescript
{
  name: 'popup',
  title: '{{t("Popup")}}',
  Component: 'PopupActionInitializer',
  useComponentProps() {
    return {
      'x-component': 'Action.Link',
    };
  },
}
```

**API 调用**:
- **端点**: 取决于弹窗内配置的操作
- **灵活性**: 可以配置任意操作

**实际应用**:
```typescript
// 弹窗操作调用示例
const openPopupAction = async (actionConfig, recordData) => {
  // 弹窗内的操作可以是任意类型
  switch (actionConfig.action) {
    case 'create':
      return await createRecord(actionConfig.collection, recordData);
    case 'update':
      return await updateRecord(actionConfig.collection, recordData.id, recordData);
    case 'custom':
      return await sendCustomRequest(actionConfig.requestId, recordData);
    default:
      throw new Error('Unsupported action type');
  }
};
```

### 3.5 更新记录操作 (Update Record)

**操作配置**:
```typescript
{
  name: 'update-record',
  title: '{{t("Update record")}}',
  Component: 'UpdateRecordActionInitializer',
  useVisible: () => useActionAvailable('update'),
}
```

**API 调用**:
- **端点**: `PUT /{collection}:update/{id}`
- **权限**: `update`
- **特点**: 直接更新，无需打开编辑表单

**实际应用**:
```typescript
// 快速更新记录调用示例
const quickUpdateRecord = async (collectionName, recordId, fieldUpdates) => {
  const apiClient = useAPIClient();
  return await apiClient.request({
    url: `/${collectionName}:update`,
    method: 'PUT',
    data: {
      filterByTk: recordId,
      values: fieldUpdates,
      forceUpdate: true, // 强制更新
    }
  });
};
```

### 3.6 链接操作 (Link)

**操作配置**:
```typescript
{
  name: 'link',
  title: '{{t("Link")}}',
  Component: 'LinkActionInitializer',
  useComponentProps() {
    return {
      'x-component': 'Action.Link',
    };
  },
}
```

**API 调用**:
- **端点**: 无直接 API 调用
- **作用**: 导航到指定页面

**实际应用**:
```typescript
// 链接操作调用示例
const navigateToLink = (linkConfig, recordData) => {
  const url = compile(linkConfig.url, {
    id: recordData.id,
    name: recordData.name,
    // 其他动态参数
  });
  
  if (isURL(url)) {
    window.location.href = url;
  } else {
    navigate(url);
  }
};
```

## 4. Grid Card Block 配置与创建

### 4.1 UI Schema 创建

**源码位置**: `/packages/core/client/src/modules/blocks/data-blocks/grid-card/createGridCardBlockUISchema.ts:12`

```typescript
export const createGridCardBlockUISchema = (options: {
  dataSource: string;
  collectionName?: string;
  association?: string;
  templateSchema?: ISchema;
  rowKey?: string;
}): ISchema => {
  const { collectionName, association, templateSchema, dataSource, rowKey } = options;
  const resourceName = association || collectionName;

  return {
    type: 'void',
    'x-acl-action': `${resourceName}:view`,
    'x-decorator': 'GridCard.Decorator',
    'x-use-decorator-props': 'useGridCardBlockDecoratorProps',
    'x-decorator-props': {
      collection: collectionName,
      association,
      dataSource,
      readPretty: true,
      action: 'list',
      params: {
        pageSize: 12, // Grid Card 默认分页大小
      },
      runWhenParamsChanged: true,
      rowKey,
    },
    'x-component': 'BlockItem',
    'x-use-component-props': 'useGridCardBlockItemProps',
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:gridCard',
    properties: {
      actionBar: {
        type: 'void',
        'x-initializer': 'gridCard:configureActions',
        'x-component': 'ActionBar',
        'x-use-component-props': 'useGridCardActionBarProps',
      },
      list: {
        type: 'array',
        'x-component': 'GridCard',
        'x-use-component-props': 'useGridCardBlockProps',
        properties: {
          item: {
            type: 'object',
            'x-component': 'GridCard.Item',
            'x-read-pretty': true,
            'x-use-component-props': 'useGridCardItemProps',
            properties: {
              grid: templateSchema || {
                type: 'void',
                'x-component': 'Grid',
                'x-initializer': 'details:configureFields',
              },
              actionBar: {
                type: 'void',
                'x-align': 'left',
                'x-initializer': 'gridCard:configureItemActions',
                'x-component': 'ActionBar',
                'x-use-component-props': 'useGridCardActionBarProps',
                'x-component-props': {
                  layout: 'one-column',
                },
              },
            },
          },
        },
      },
    },
  };
};
```

### 4.2 通过 API 创建 Grid Card Block

```typescript
// 创建 Grid Card Block 调用示例
const createGridCardBlock = async (targetUid, blockConfig) => {
  const apiClient = useAPIClient();
  
  return await apiClient.request({
    url: `/uiSchemas:insertAdjacent/${targetUid}`,
    method: 'POST',
    data: {
      position: 'beforeEnd',
      schema: {
        type: 'void',
        title: blockConfig.title || 'Grid Card',
        'x-decorator': 'GridCard.Decorator',
        'x-use-decorator-props': 'useGridCardBlockDecoratorProps',
        'x-component': 'BlockItem',
        'x-settings': 'blockSettings:gridCard',
        'x-decorator-props': {
          collection: blockConfig.collectionName,
          dataSource: blockConfig.dataSource,
          action: 'list',
          params: {
            pageSize: blockConfig.pageSize || 12,
          },
          runWhenParamsChanged: true,
        },
        properties: {
          actionBar: {
            type: 'void',
            'x-initializer': 'gridCard:configureActions',
            'x-component': 'ActionBar',
          },
          list: {
            type: 'array',
            'x-component': 'GridCard',
            properties: {
              item: {
                type: 'object',
                'x-component': 'GridCard.Item',
                properties: {
                  grid: {
                    type: 'void',
                    'x-component': 'Grid',
                    'x-initializer': 'details:configureFields',
                  },
                  actionBar: {
                    type: 'void',
                    'x-align': 'left',
                    'x-initializer': 'gridCard:configureItemActions',
                    'x-component': 'ActionBar',
                  },
                },
              },
            },
          },
        },
      }
    }
  });
};
```

## 5. 权限控制与 ACL

### 5.1 权限映射表

| 操作 | x-action | 权限要求 | ACL Provider |
|------|----------|----------|--------------|
| Filter | filter | 无 | 否 |
| Add New | create | create | 是 |
| Refresh | list | view | 否 |
| Import | importXlsx | importXlsx | 是 |
| Export | export | export | 是 |
| Custom Request | customize:table:request:global | 自定义 | 否 |
| View | get | get | 是 |
| Edit | update | update | 是 |
| Delete | destroy | destroy | 是 |
| Update Record | update | update | 是 |
| Popup | 弹窗内操作 | 根据配置 | 否 |
| Link | navigation | 无 | 否 |

### 5.2 ACL 权限检查

```typescript
// 权限检查 Hook
const useActionAvailable = (action: string) => {
  const collection = useCollection();
  const { unavailableActions, availableActions } = collection?.options || {};
  
  if (availableActions) {
    return availableActions.includes(action);
  }
  
  if (unavailableActions) {
    return !unavailableActions.includes(action);
  }
  
  return true;
};
```

## 6. 实际应用示例

### 6.1 完整的 Grid Card Block 配置

```typescript
// 完整配置示例
const gridCardConfig = {
  // 块配置
  title: '产品展示',
  collectionName: 'products',
  dataSource: 'main',
  pageSize: 12,
  
  // 块级别操作
  blockActions: [
    {
      name: 'filter',
      title: '筛选',
      enabled: true,
    },
    {
      name: 'addNew',
      title: '新增产品',
      enabled: true,
      requiresAuth: true,
    },
    {
      name: 'refresh',
      title: '刷新',
      enabled: true,
    },
    {
      name: 'export',
      title: '导出',
      enabled: true,
      requiresAuth: true,
    },
  ],
  
  // 卡片级别操作
  itemActions: [
    {
      name: 'view',
      title: '查看',
      enabled: true,
    },
    {
      name: 'edit',
      title: '编辑',
      enabled: true,
      requiresAuth: true,
    },
    {
      name: 'delete',
      title: '删除',
      enabled: true,
      requiresAuth: true,
      confirm: true,
    },
  ],
  
  // 字段配置
  fields: [
    {
      name: 'name',
      title: '产品名称',
      component: 'Input',
    },
    {
      name: 'price',
      title: '价格',
      component: 'InputNumber',
    },
    {
      name: 'image',
      title: '图片',
      component: 'Image',
    },
  ],
};

// 创建 Grid Card Block
const createProductGridCard = async (targetUid) => {
  return await createGridCardBlock(targetUid, gridCardConfig);
};
```

### 6.2 动态配置示例

```typescript
// 根据用户角色动态配置操作
const configureGridCardByRole = async (targetUid, userRole) => {
  const apiClient = useAPIClient();
  const blockActions = [];
  const itemActions = [];
  
  // 基础操作（所有用户）
  blockActions.push({
    name: 'filter',
    title: '筛选',
    enabled: true,
  });
  
  blockActions.push({
    name: 'refresh',
    title: '刷新',
    enabled: true,
  });
  
  itemActions.push({
    name: 'view',
    title: '查看',
    enabled: true,
  });
  
  // 根据角色添加操作
  if (userRole === 'admin' || userRole === 'manager') {
    blockActions.push({
      name: 'addNew',
      title: '新增',
      enabled: true,
    });
    
    blockActions.push({
      name: 'import',
      title: '导入',
      enabled: true,
    });
    
    itemActions.push({
      name: 'edit',
      title: '编辑',
      enabled: true,
    });
  }
  
  if (userRole === 'admin') {
    blockActions.push({
      name: 'export',
      title: '导出',
      enabled: true,
    });
    
    itemActions.push({
      name: 'delete',
      title: '删除',
      enabled: true,
    });
  }
  
  // 创建配置好的块
  return await createGridCardBlock(targetUid, {
    collectionName: 'products',
    dataSource: 'main',
    blockActions,
    itemActions,
  });
};
```

## 7. 性能优化建议

### 7.1 分页优化

```typescript
// 优化分页参数
const optimizedGridCardParams = {
  pageSize: 12, // 合理的页面大小
  page: 1,
  fields: ['id', 'name', 'price', 'image'], // 只请求必要字段
  appends: ['category'], // 预加载关联数据
  filter: {}, // 应用筛选条件
  sort: ['-createdAt'], // 排序规则
};
```

### 7.2 缓存策略

```typescript
// 使用缓存减少重复调用
const getGridCardData = async (collectionName, params) => {
  const cacheKey = `gridCard_${collectionName}_${JSON.stringify(params)}`;
  const cached = sessionStorage.getItem(cacheKey);
  
  if (cached) {
    return JSON.parse(cached);
  }
  
  const apiClient = useAPIClient();
  const response = await apiClient.request({
    url: `/${collectionName}:list`,
    method: 'GET',
    params,
  });
  
  // 缓存结果（5分钟过期）
  sessionStorage.setItem(cacheKey, JSON.stringify(response.data));
  
  return response.data;
};
```

### 7.3 批量操作

```typescript
// 批量更新卡片操作
const batchUpdateCardActions = async (blockUid, actions) => {
  const apiClient = useAPIClient();
  
  // 使用事务确保一致性
  const transaction = await apiClient.request({
    url: '/transactions:start',
    method: 'POST'
  });
  
  try {
    const results = [];
    for (const action of actions) {
      const result = await apiClient.request({
        url: `/uiSchemas:insertAdjacent/${blockUid}`,
        method: 'POST',
        data: {
          position: 'beforeEnd',
          schema: action.schema,
        },
        headers: {
          'X-Transaction-ID': transaction.data.id
        }
      });
      results.push(result);
    }
    
    await apiClient.request({
      url: `/transactions:commit/${transaction.data.id}`,
      method: 'POST'
    });
    
    return results;
  } catch (error) {
    await apiClient.request({
      url: `/transactions:rollback/${transaction.data.id}`,
      method: 'POST'
    });
    throw error;
  }
};
```

## 8. 错误处理和调试

### 8.1 常见错误及解决方案

| 错误类型 | 可能原因 | 解决方案 |
|---------|----------|----------|
| 401 Unauthorized | 认证失败 | 检查 JWT token 是否有效 |
| 403 Forbidden | 权限不足 | 检查用户权限配置 |
| 404 Not Found | 资源不存在 | 验证集合名称和关联配置 |
| 422 Unprocessable Entity | 参数错误 | 检查请求参数格式 |
| 500 Internal Server Error | 服务器错误 | 检查服务器日志 |

### 8.2 调试工具

```typescript
// 调试 Grid Card API 调用
const debugGridCardApi = async (collectionName, operation, params) => {
  console.log('Grid Card API Request:', {
    collectionName,
    operation,
    params,
    timestamp: new Date().toISOString(),
  });
  
  try {
    const apiClient = useAPIClient();
    const response = await apiClient.request({
      url: `/${collectionName}:${operation}`,
      method: operation === 'list' ? 'GET' : 'POST',
      data: params,
    });
    
    console.log('Grid Card API Response:', {
      status: response.status,
      data: response.data,
      timestamp: new Date().toISOString(),
    });
    
    return response;
  } catch (error) {
    console.error('Grid Card API Error:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
      timestamp: new Date().toISOString(),
    });
    throw error;
  }
};
```

## 9. 总结

### 9.1 Grid Card Block 操作特点

1. **响应式设计**: 自动适应不同屏幕尺寸
2. **丰富的操作**: 支持块级别和卡片级别的多种操作
3. **权限控制**: 细粒度的 ACL 权限管理
4. **灵活配置**: 可通过 UI Schema 动态配置
5. **性能优化**: 内置分页、缓存和批量操作支持

### 9.2 API 调用模式

1. **统一端点**: 所有操作都通过标准 RESTful API
2. **参数标准化**: 使用统一的参数格式（filterByTk, values, filter 等）
3. **权限集成**: 与 ACL 系统深度集成
4. **错误处理**: 标准化的错误响应格式

### 9.3 最佳实践

1. **合理使用分页**: Grid Card 默认页面大小为 12，适合卡片展示
2. **字段优化**: 只请求必要的字段，减少数据传输
3. **权限配置**: 根据用户角色合理配置操作权限
4. **缓存策略**: 对于静态数据使用缓存提升性能
5. **错误处理**: 实现完善的错误处理机制

Grid Card Block 为 NocoBase 提供了强大的数据展示和操作能力，通过合理的 API 设计和权限控制，可以满足各种业务场景的需求。开发者可以基于这份文档准确理解和使用 Grid Card Block 的各种功能。

---

**报告日期**: 2025-01-09  
**版本**: NocoBase 最新版本  
**作者**: Claude Code Assistant  

**注意**: 本报告基于 NocoBase 最新源码分析，代码示例已与实际实现保持一致。