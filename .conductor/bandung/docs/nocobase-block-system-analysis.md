# NocoBase 区块系统深度分析报告

## 📋 执行摘要

通过对 NocoBase 区块系统的深入实践研究，我们发现了页面创建和区块管理的正确方法，并详细分析了各种区块类型的结构和配置选项。这些发现为我们的 MCP 工具开发提供了重要指导。

## 🎯 关键发现

### ✅ 正确的页面创建方法
- **官方界面创建**：使用 NocoBase 的"Add menu item" -> "Page"功能
- **简单结构**：页面自动获得简单的 Page 组件结构
- **自动初始化**：NocoBase 自动处理 Grid 初始化和区块添加功能

### ❌ 错误的方法（我们之前的做法）
- 直接通过 API 创建复杂的嵌套 schema 结构
- 手动创建 Grid 和区块结构
- 导致前端无法正确渲染

## 📊 区块类型详细分析

### 1. Markdown 区块 (`Markdown.Void`)

**基本特征**：
- 简单的内容展示区块
- 支持完整的 Markdown 语法
- 支持 Handlebars 模板引擎
- 可插入动态变量

**配置选项**：
- ✅ Edit markdown - 编辑内容
- ✅ Set block height - 设置高度
- ✅ Block Linkage rules - 联动规则
- ✅ Template engine - 模板引擎选择
- ✅ Delete - 删除区块

**Schema 结构**：
```json
{
  "type": "void",
  "x-component": "Markdown.Void",
  "x-component-props": {
    "content": "markdown content here"
  }
}
```

### 2. Table 区块 (`CardItem` + `TableV2`)

**基本特征**：
- 数据驱动的区块类型
- 需要选择数据集合（Collection）
- 支持完整的 CRUD 操作
- 高度可配置的显示和交互选项

**配置选项**：
- ✅ Edit block title & description - 编辑标题描述
- ✅ Set block height - 设置高度
- ✅ Block Linkage rules - 联动规则
- ✅ Enable drag and drop sorting - 拖拽排序
- ✅ Enable index column - 索引列
- ✅ Set the data scope - 数据范围
- ✅ Set default sorting rules - 默认排序
- ✅ Set data loading mode - 数据加载模式
- ✅ Records per page - 每页记录数
- ✅ Table size - 表格大小
- ✅ Connect data blocks - 连接数据区块
- ✅ Save as template - 保存为模板
- ✅ Delete - 删除区块

**字段配置**：
- 动态字段选择（基于 Collection Schema）
- 每个字段都有独立的显示/隐藏开关
- 支持字段排序和自定义显示

**示例字段**（Users 集合）：
- ID, Nickname, Username, Email, Phone
- Password, Created at, Last updated at
- Roles, Created by, Last updated by
- Action column（操作列）

### 3. Charts 区块 (`ChartCardItem`)

**基本特征**：
- 复杂的图表容器区块
- 支持多种图表类型
- 有自己的子区块系统
- 支持数据可视化

**特殊功能**：
- Configure actions - 配置操作按钮
- Add block - 在图表内添加子区块
- 支持嵌套的区块结构

### 4. Iframe 区块 (`Iframe`)

**基本特征**：
- 嵌入外部网页内容
- 需要配置 URL
- 简单的容器类型区块

**默认状态**：
- 显示提示："Please fill in the iframe URL"
- 需要通过设置配置 URL

## 🔧 区块设计器控件模式

所有区块都遵循统一的设计器控件模式：

### 标准控件
1. **designer-drag-handler** - 拖拽控制器
2. **designer-schema-initializer** - 初始化器
3. **designer-schema-settings** - 设置菜单

### 命名规范
```
designer-{type}-{component}-{settings}:{context}
```

示例：
- `designer-schema-settings-Markdown.Void-blockSettings:markdown`
- `designer-schema-settings-CardItem-blockSettings:table-users`

## 📋 通用配置选项

### 基础配置
- **Set block height** - 所有区块都支持高度设置
- **Block Linkage rules** - 区块间联动规则
- **Delete** - 删除区块

### 模板功能
- **Save as reference template** - 保存为引用模板
- **Save as inherited template** - 保存为继承模板

### 数据区块特有
- **Set the data scope** - 数据范围设置
- **Connect data blocks** - 区块间数据连接
- **Configure actions** - 操作按钮配置
- **Configure columns/fields** - 字段配置

## 🚀 最佳实践建议

### 1. 页面创建
- ✅ 使用 NocoBase 官方界面创建页面
- ✅ 让 NocoBase 自动处理页面初始化
- ❌ 避免手动创建复杂的 schema 结构

### 2. 区块添加
- ✅ 使用"Add block"按钮添加区块
- ✅ 根据需求选择合适的区块类型
- ✅ 配置完成后再添加下一个区块

### 3. 数据区块配置
- ✅ 先选择数据集合
- ✅ 配置显示字段
- ✅ 设置适当的分页和排序
- ✅ 根据需要配置操作按钮

## 🛠️ MCP 工具改进建议

### 1. 页面创建工具
- 使用简单的 Page 组件结构
- 避免复杂的嵌套 schema
- 参考正确的页面创建 API 调用

### 2. 区块模板更新
- 更新 `block-templates.ts` 中的模板
- 确保模板符合 NocoBase 的标准结构
- 添加更多区块类型支持

### 3. 配置选项支持
- 支持区块的常用配置选项
- 提供字段选择功能（数据区块）
- 支持模板保存和加载

### 4. 错误处理
- 改进错误提示和处理
- 验证区块创建的正确性
- 提供回滚机制

## 📈 下一步行动计划

1. **立即行动**：
   - 修复页面创建逻辑
   - 更新区块模板
   - 测试基本功能

2. **短期目标**：
   - 支持更多区块类型
   - 添加配置选项支持
   - 改进用户体验

3. **长期目标**：
   - 完整的区块管理系统
   - 模板系统集成
   - 高级配置功能

## 🎯 Table Actions 深度分析

### 📋 Table Actions 完整列表

通过实际测试，我们发现 Table 区块支持非常丰富的 Actions：

#### 🔍 过滤和搜索
1. **Filter** - 过滤器（开关控制）

#### ➕ 数据操作
2. **Add new** - 添加新记录
3. **Popup** - 弹窗操作
4. **Delete** - 删除操作（开关控制）

#### 🔄 界面操作
5. **Refresh** - 刷新数据（开关控制）
6. **Link** - 链接操作

#### 🛠️ 高级操作
7. **Custom request** - 自定义请求
8. **Bulk edit** - 批量编辑
9. **Bulk update** - 批量更新

#### 📊 数据导入导出
10. **Export** - 导出数据
11. **Import** - 导入数据

### 🎭 Popup 容器深度分析

**Popup 的重要发现**：
- Popup 本身就是一个完整的容器系统
- 有自己的路由：`/popups/{popup-id}`
- 支持 Tab 系统，可以添加多个标签页
- 每个 Tab 都可以添加区块
- 支持的区块类型有限制（主要是表单相关）

**Popup 中可用的区块类型**：
- **Data blocks**: Form（表单区块）
- **Other blocks**: Markdown, Iframe

### 📝 Form 区块深度分析

**Form 区块的完整结构**：

#### 🏗️ 基本结构
- **区块类型**：`block-item-CardItem-users-form`
- **设计器控件**：完整的拖拽、初始化、设置控件
- **数据源选择**：Current collection, Other collections

#### 📊 字段配置系统
**字段配置选项**：
- 搜索功能
- 11个标准字段（ID, Nickname, Username, Email, Phone, Password, Created at, Last updated at, Roles, Created by, Last updated by）
- 每个字段都有独立的开关控制
- 支持添加 Markdown 字段
- 支持添加字段组

**字段结构**：
- **字段区块**：`block-item-CollectionField-{collection}-form-{field}-{FieldName}`
- **设计器控件**：`designer-{type}-CollectionField-fieldSettings:FormItem-{collection}-{field}`
- **显示格式**：标签 + 输入框，必填字段带 "*" 标记

#### ⚡ Actions 配置
**Form Actions 类型**：
1. **Submit** - 提交表单
2. **Custom request** - 自定义请求

### 🔧 设计器控件统一模式

**所有区块都遵循统一的设计器控件模式**：

#### 标准控件
1. **designer-drag-handler** - 拖拽控制器
2. **designer-schema-initializer** - 初始化器
3. **designer-schema-settings** - 设置菜单

#### 命名规范
```
designer-{type}-{component}-{settings}:{context}
```

**示例**：
- Table: `designer-schema-settings-CardItem-blockSettings:table-users`
- Form: `designer-schema-settings-CardItem-blockSettings:createForm-users`
- Field: `designer-schema-settings-CollectionField-fieldSettings:FormItem-users-users.username`

## 🎯 结论

通过这次深入研究，我们完全理解了 NocoBase 区块系统的工作机制。关键发现包括：

### ✅ 核心发现
1. **页面创建的正确方法**：使用 NocoBase 官方界面，获得简单的 Page 组件结构
2. **区块系统的层次结构**：Page -> Grid -> Blocks -> Fields/Actions
3. **Popup 容器系统**：完整的弹窗容器，支持 Tab 和区块嵌套
4. **统一的设计器模式**：所有组件都遵循相同的设计器控件模式
5. **丰富的配置选项**：每种区块都有详细的配置选项和字段管理

### 🚀 对 MCP 工具的指导意义
- 遵循 NocoBase 的设计模式，而不是试图绕过它们
- 理解区块的层次结构和依赖关系
- 支持完整的配置选项，而不仅仅是基本功能
- 实现正确的 Schema 结构和命名规范

## 🎭 Table Actions 列深度分析

### 📋 Actions 列配置完整列表

通过实际测试，我们发现 Table Actions 列支持 8 种不同的操作类型：

#### 🔍 查看和编辑操作
1. **View** - 查看记录详情页面
   - 区块类型：`action-Action.Link-View-view-{collection}-table-{index}`
   - 设计器控件：`designer-drag-handler-Action.Link-actionSettings:view-{collection}`
   - 功能：打开记录详情页面

2. **Edit** - 编辑记录
   - 区块类型：`action-Action.Link-Edit-update-{collection}-table-{index}`
   - 设计器控件：`designer-drag-handler-Action.Link-actionSettings:edit-{collection}`
   - 图标：编辑图标
   - 功能：打开编辑弹窗，URL 格式：`/popups/{popup-id}/filterbytk/{record-id}`

#### 🗑️ 删除操作
3. **Delete** - 删除记录
   - 特殊功能：带开关控制，可以启用/禁用
   - 危险操作，需要确认

#### 🎭 弹窗和更新操作
4. **Popup** - 弹窗操作
   - 区块类型：`action-Action.Link-Popup-customize:popup-{collection}-table-{index}`
   - 设计器控件：`designer-drag-handler-Action.Link-actionSettings:popup-{collection}`
   - 功能：打开自定义弹窗

5. **Update record** - 更新记录
   - 功能：直接更新记录数据

#### 🔗 链接和请求操作
6. **Custom request** - 自定义请求
   - 功能：发送自定义 API 请求

7. **Link** - 链接操作
   - 功能：跳转到指定链接

#### 📋 复制操作
8. **Duplicate** - 复制记录
   - 功能：复制当前记录创建新记录

### 🎯 Edit 操作的弹窗系统深度分析

**Edit 操作的完整工作流程**：

#### 🌐 URL 路由系统
- **弹窗 URL 格式**：`/popups/{popup-id}/filterbytk/{record-id}`
- **示例**：`/popups/c5okuk2dqbj/filterbytk/1`
- **参数说明**：
  - `popup-id`：弹窗的唯一标识符
  - `record-id`：要编辑的记录 ID

#### 🏗️ 弹窗结构
1. **Tab 系统**：
   - 默认有一个"Edit"标签页
   - 支持添加更多标签页
   - Tab 配置：`schema-initializer-Tabs-popup:addTab-{collection}`

2. **区块容器**：
   - 支持添加多种类型的区块
   - Add block 按钮：`schema-initializer-Grid-popup:common:addBlock-{collection}`

#### 📊 Edit 弹窗支持的区块类型

**Data blocks（数据区块）**：
1. **Details** - 详情区块
2. **Form (Edit)** - 编辑表单（最适合编辑操作）
3. **Form (Add new)** - 新增表单
4. **Table** - 表格
5. **List** - 列表
6. **Grid Card** - 网格卡片
7. **Calendar** - 日历
8. **Charts** - 图表
9. **Gantt** - 甘特图
10. **Kanban** - 看板

**Filter blocks（过滤区块）**：
11. **Form** - 过滤表单
12. **Collapse** - 折叠面板

**Other blocks（其他区块）**：
13. **Markdown** - Markdown 区块
14. **Iframe** - 内嵌框架
15. **Action panel** - 操作面板

### 🔧 Actions 列的设计模式

**统一的命名规范**：
```
action-Action.Link-{ActionType}-{context}-{collection}-table-{index}
```

**设计器控件模式**：
```
designer-{type}-Action.Link-actionSettings:{actionType}-{collection}
```

**示例**：
- Edit: `designer-drag-handler-Action.Link-actionSettings:edit-users`
- View: `designer-schema-settings-Action.Link-actionSettings:view-users`
- Popup: `designer-schema-settings-Action.Link-actionSettings:popup-users`

## 🔍 Schema API 深度分析

### 📋 API 端点发现

通过网络请求分析和实际测试，我们发现了 NocoBase 的核心 Schema API：

#### 1. 获取页面基本 Schema
```
GET /api/uiSchemas:getJsonSchema/{schemaUid}
```

**功能**: 获取页面的基本信息
**响应**: 页面的基本属性（组件类型、UID、名称等）

#### 2. 获取完整 Schema 结构
```
GET /api/uiSchemas:getProperties/{schemaUid}
```

**功能**: 获取页面的完整嵌套结构
**响应**: 包含所有子组件、区块、配置的完整 JSON 结构

#### 3. Schema 操作 API
```
POST /api/uiSchemas:insertAdjacent    # 插入相邻组件
POST /api/uiSchemas:patch             # 更新组件
POST /api/uiSchemas:remove            # 删除组件
POST /api/uiSchemas:insert            # 插入组件
```

### 🏗️ Schema 结构深度解析

**完整的页面层次结构**：
```
📄 Page
└── 🏗️ Grid (页面主容器)
    ├── 📏 Grid.Row (网格行)
    │   ├── 📐 Grid.Col (网格列)
    │   │   └── 📦 Block (具体区块)
    │   └── 📐 Grid.Col
    │       └── 📦 Block
    └── 📏 Grid.Row
        └── 📐 Grid.Col
            └── 📊 TableV2 (表格区块)
                ├── 🎛️ ActionBar (操作栏)
                │   └── 🔘 Actions (操作按钮)
                └── 📋 Columns (表格列)
                    ├── 🏷️ Data Columns (数据列)
                    └── 🔗 Actions Column (操作列)
                        ├── 🔗 Popup Action
                        ├── 🔗 View Action
                        └── 🔗 Edit Action
```

### 🎯 关键属性系统

**组件标识**：
- `x-uid`: 全局唯一标识符
- `x-component`: 组件类型
- `x-decorator`: 装饰器类型

**数据绑定**：
- `x-collection-field`: 集合字段绑定
- `x-data-source`: 数据源标识
- `x-acl-action`: 权限控制

**配置系统**：
- `x-settings`: 设置菜单标识
- `x-initializer`: 初始化器标识
- `x-toolbar`: 工具栏标识

**属性配置**：
- `x-component-props`: 组件属性
- `x-decorator-props`: 装饰器属性

### 🚀 MCP 工具集成策略

基于 Schema API 的发现，我们的 MCP 工具可以实现：

#### 1. 页面分析能力
- **完整结构获取**: 通过 `getProperties` API 获取页面的完整区块结构
- **智能区块识别**: 基于 `x-component` 和 `x-decorator` 识别区块类型
- **层次关系理解**: 理解 Grid 系统的行列结构和区块嵌套

#### 2. 精确操作能力
- **UID 定位**: 使用 `x-uid` 精确定位任何组件
- **类型感知操作**: 根据组件类型提供相应的操作选项
- **配置完整性**: 支持完整的组件属性和装饰器配置

#### 3. 动态管理能力
- **实时结构同步**: 通过 API 获取最新的页面结构
- **增量更新**: 支持添加、修改、删除特定区块
- **关系维护**: 在操作时保持正确的父子关系

这些发现为我们提供了明确的改进方向，确保我们的工具能够正确、高效地与 NocoBase 集成。
