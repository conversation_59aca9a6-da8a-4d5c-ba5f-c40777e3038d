# 🔄 NocoBase 双重验证策略：Schema API + Playwright

## 🎯 核心理念

当我们进行前端页面操作后，可以通过 **Schema API** 对页面结构进行获取和分析，从而验证正确性。这种方式可以跟使用 **Playwright MCP** 的方式形成互补，提供完整的验证覆盖。

## 📊 两种验证方式的互补关系

### 🔍 Schema API 验证 - 结构化验证
**优势**：
- ✅ **精确的结构验证**: 获取完整的 JSON Schema，验证组件层次结构
- ✅ **配置完整性检查**: 验证 `x-component-props`、`x-decorator-props` 等配置
- ✅ **数据绑定验证**: 确认集合绑定、字段映射、权限设置
- ✅ **UID 精确定位**: 通过唯一标识符精确验证特定组件
- ✅ **快速响应**: API 调用速度快，适合自动化测试

**适用场景**：
- 验证区块是否正确创建
- 检查组件配置是否正确
- 确认数据源绑定
- 验证权限设置
- 检查组件层次结构

### 🎭 Playwright 验证 - 视觉和交互验证
**优势**：
- ✅ **真实用户体验**: 验证页面的实际渲染效果
- ✅ **交互功能测试**: 测试按钮点击、表单提交、数据加载
- ✅ **视觉元素检查**: 验证样式、布局、颜色、字体
- ✅ **跨浏览器兼容**: 确保在不同浏览器中的一致性
- ✅ **端到端测试**: 完整的用户操作流程验证

**适用场景**：
- 验证页面视觉效果
- 测试用户交互功能
- 检查数据加载和显示
- 验证表单提交流程
- 测试响应式布局

## 🔄 完整验证工作流

### 1. 操作前准备
```typescript
// 记录操作前的页面状态
const beforeSchema = await client.getSchemaProperties(pageSchemaUid);
const beforeSnapshot = await playwright.screenshot();
```

### 2. 执行操作
```typescript
// 通过 MCP 工具执行操作
await addTableBlock({
  pageSchemaUid: 'cafh7yoyd6w',
  collectionName: 'users',
  title: 'User Management'
});
```

### 3. Schema API 验证
```typescript
// 验证区块结构
const schemaResult = await verifyBlockCreation(
  client,
  pageSchemaUid,
  'table',
  {
    expectedCount: 1,
    collectionName: 'users'
  }
);

console.log(schemaResult.message);
// ✅ Found 1 table block(s) bound to 'users'
```

### 4. Playwright 验证
```typescript
// 验证视觉效果和交互
const playwrightResult = await verifyWithPlaywright({
  selector: '[data-testid="table-block"]',
  expectedText: 'User Management',
  shouldExist: true
});

// 测试交互功能
await playwright.click('[data-testid="add-new-button"]');
await playwright.waitForSelector('[data-testid="create-form"]');
```

### 5. 综合评估
```typescript
const completeResult = await completeVerification(client, pageSchemaUid, {
  blockType: 'table',
  blockOptions: { expectedCount: 1, collectionName: 'users' },
  playwright: { selector: '[data-testid="table-block"]', expectedCount: 1 }
});

console.log(completeResult.summary);
// 🎉 Complete verification passed: ✅ Found 1 table block(s) bound to 'users'; ✅ Playwright verification passed
```

## 📋 验证场景示例

### 场景 1: 表格区块创建验证
```typescript
// Schema API 验证
const schemaCheck = await verifyBlockCreation(client, pageUid, 'table', {
  expectedCount: 1,
  collectionName: 'users'
});

// Playwright 验证
const visualCheck = await playwright.evaluate(() => {
  const table = document.querySelector('.ant-table');
  const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent);
  return { exists: !!table, headers };
});
```

### 场景 2: 表单字段配置验证
```typescript
// Schema API 验证字段配置
const fieldSchema = foundBlocks[0].properties.grid.properties;
const hasUsernameField = Object.values(fieldSchema).some(field => 
  field['x-collection-field'] === 'users.username'
);

// Playwright 验证字段显示
const fieldVisible = await playwright.isVisible('input[placeholder*="username"]');
```

### 场景 3: Actions 列配置验证
```typescript
// Schema API 验证 Actions 配置
const actionsColumn = foundBlocks[0].properties.actions;
const hasEditAction = Object.values(actionsColumn.properties).some(action =>
  action['x-action'] === 'update'
);

// Playwright 验证 Actions 按钮
const editButtonExists = await playwright.isVisible('[data-testid="edit-action"]');
```

## 🚀 最佳实践

### 1. 分层验证策略
- **第一层**: Schema API 快速验证结构正确性
- **第二层**: Playwright 验证关键交互功能
- **第三层**: 端到端业务流程测试

### 2. 错误处理和重试
```typescript
async function robustVerification(client, pageUid, config) {
  let attempts = 0;
  const maxAttempts = 3;
  
  while (attempts < maxAttempts) {
    try {
      const result = await completeVerification(client, pageUid, config);
      if (result.overall) return result;
      
      // 等待页面更新
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;
    } catch (error) {
      if (attempts === maxAttempts - 1) throw error;
      attempts++;
    }
  }
}
```

### 3. 性能优化
- 优先使用 Schema API 进行快速验证
- 只在必要时使用 Playwright 进行视觉验证
- 批量验证多个组件以减少 API 调用

### 4. 测试数据管理
- 使用独立的测试环境
- 每次测试前清理页面状态
- 使用可预测的测试数据

## 🎯 验证策略的价值

1. **可靠性**: 双重验证确保操作的正确性和完整性
2. **效率**: Schema API 提供快速反馈，Playwright 提供深度验证
3. **覆盖面**: 从数据结构到用户体验的全方位验证
4. **可维护性**: 结构化的验证方法便于维护和扩展
5. **调试友好**: 详细的验证结果帮助快速定位问题

这种双重验证策略为 NocoBase MCP 工具提供了强大的质量保证机制！
