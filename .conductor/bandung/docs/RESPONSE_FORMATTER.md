# NocoBase MCP 响应格式化器

## 📋 概述

响应格式化器是为 NocoBase MCP 工具设计的智能数据压缩和格式化系统，能够将大量的 JSON 数据转换为高度压缩、易读的 YAML 格式。

## 🎯 主要功能

### 1. 智能压缩阈值
- **小数据**（< 5KB）：保持原始 JSON 格式，确保简单数据的可读性
- **大数据**（≥ 5KB）：自动触发压缩和分析，生成结构化的 YAML 输出

### 2. 元数据分析 (`_meta` 区域)
- **数据统计**：原始大小、节点数量、上下文信息
- **常见属性分析**：识别高频出现的冗余字段
- **结构模式检测**：发现数据中的规律和模式

### 3. 核心语义结构 (`layout` 区域)
- **Schema 压缩**：提取组件层次结构，过滤技术细节
- **列表数据摘要**：显示分页信息和样本数据
- **集合信息概览**：展示集合基本信息和关键字段

## 🚀 使用方法

### 基础用法

```typescript
import { createFormattedResponse, createFormattedErrorResponse } from '../utils/response-formatter.js';

// 成功响应
const response = createFormattedResponse(
  'Data retrieved successfully:',
  data,
  'schema' // 可选：指定数据类型上下文
);

// 错误响应
const errorResponse = createFormattedErrorResponse(error);
```

### 支持的数据类型上下文

- `'schema'` - NocoBase UI Schema 数据
- `'list'` - 列表/分页数据
- `'collection'` - 集合定义数据
- `'details'` - 详情记录数据
- `'routes'` - 路由数据

## 📊 格式化示例

### 原始 JSON 数据 (87.4KB)
```json
{
  "_isJSONSchemaObject": true,
  "version": "2.0",
  "type": "void",
  "x-component": "Page",
  "x-uid": "cafh7yoyd6w",
  "x-async": false,
  "x-app-version": "1.7.10",
  "properties": {
    // ... 大量重复的嵌套结构
  }
}
```

### 格式化后的 YAML 输出 (60.2KB, 压缩 31.1%)
```yaml
_meta:
  source_info:
    original_size_kb: 87.4
    total_nodes: 902
    context: schema
  common_properties:
    - property: _isJSONSchemaObject
      value: true
      occurrences: 401
      notes: '高频出现的基础属性, 布尔值: true'
    - property: version
      value: '2.0'
      occurrences: 401
      notes: 高频出现的基础属性
  structural_patterns:
    - pattern: Random Node Keys
      description: 使用随机生成的字符串作为对象键
      format: 10-15位小写字母和数字的组合

layout:
  type: void
  component: Page
  children:
    table_block_1:
      type: void
      component: CardItem
      decorator: TableBlockProvider
      decoratorConfig:
        collection: users
        dataSource: main
        showIndex: true
        dragSort: false
```

## ⚙️ 配置选项

### ResponseFormatter 构造函数选项

```typescript
const formatter = new ResponseFormatter({
  enableMetaAnalysis: true,    // 是否启用元数据分析
  maxSamples: 3,              // 最大样本数量
  minOccurrences: 3,          // 最小出现次数（用于识别常见属性）
  compressThreshold: 5        // 压缩阈值（KB）
});
```

## 🔧 集成到现有工具

### 更新现有的 MCP 工具

```typescript
// 之前的代码
return {
  content: [{
    type: 'text',
    text: `Data retrieved:\n${JSON.stringify(data, null, 2)}`
  }]
};

// 更新后的代码
return createFormattedResponse(
  'Data retrieved successfully:',
  data,
  'appropriate-context'
);
```

### 错误处理

```typescript
// 之前的代码
return {
  content: [{
    type: 'text',
    text: `Error: ${error.message}`
  }],
  isError: true
};

// 更新后的代码
return createFormattedErrorResponse(error);
```

## 📈 性能优势

### 压缩效果
- **Schema 数据**：通常可压缩 30-50%
- **列表数据**：根据重复度可压缩 20-40%
- **集合数据**：提供结构化摘要，大幅减少冗余信息

### 可读性提升
- **YAML 格式**：比 JSON 更易读
- **层次结构**：清晰的组件关系
- **元数据洞察**：了解数据模式和特征

## 🛠️ 开发和测试

### 运行测试
```bash
# 测试格式化器功能
node scripts/test-formatter-simple.js

# 测试 MCP 工具集成
node scripts/test-mcp-with-formatter.js
```

### 自定义格式化逻辑

可以通过继承 `ResponseFormatter` 类来添加自定义的数据类型支持：

```typescript
class CustomResponseFormatter extends ResponseFormatter {
  protected compressCustomData(data: any): any {
    // 自定义压缩逻辑
    return {
      custom_summary: {
        // 自定义摘要结构
      }
    };
  }
}
```

## 📝 最佳实践

1. **选择合适的上下文**：为不同类型的数据指定正确的上下文参数
2. **保持一致性**：在整个项目中统一使用格式化器
3. **监控压缩效果**：关注压缩比和可读性的平衡
4. **错误处理**：始终使用 `createFormattedErrorResponse` 处理错误

## 🔮 未来扩展

- 支持更多数据类型的智能识别
- 可配置的压缩策略
- 交互式数据探索功能
- 多语言支持的元数据分析

---

通过使用响应格式化器，NocoBase MCP 工具现在能够提供更加清晰、简洁和有洞察力的数据展示，大大提升了开发者的使用体验。
