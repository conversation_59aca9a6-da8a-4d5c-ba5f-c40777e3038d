# NocoBase 表格操作 MCP 工具使用指南

## 概述

本指南介绍如何通过 MCP (Model Context Protocol) 协议使用 NocoBase 表格操作工具。这些工具允许 AI 客户端（如 Claude Desktop）直接操作 NocoBase 表格的各个方面。

## 🚀 快速开始

### 1. 启动 MCP 服务器

```bash
# 使用命令行参数
npm start -- --base-url https://your-nocobase.com/api --token your-token --app your-app

# 或使用环境变量
export NOCOBASE_BASE_URL=https://your-nocobase.com/api
export NOCOBASE_TOKEN=your-token
export NOCOBASE_APP=your-app
npm start
```

### 2. 在 Claude Desktop 中配置

在 Claude Desktop 的配置文件中添加：

```json
{
  "mcpServers": {
    "nocobase": {
      "command": "node",
      "args": ["/path/to/mcp-server-nocobase/dist/index.js"],
      "env": {
        "NOCOBASE_BASE_URL": "https://your-nocobase.com/api",
        "NOCOBASE_TOKEN": "your-token",
        "NOCOBASE_APP": "your-app"
      }
    }
  }
}
```

## 🛠️ 可用的 MCP 工具

### 1. add_table_action - 添加表格操作按钮

为表格添加操作按钮，支持预定义类型和自定义配置。

**AI 使用示例：**
```
AI: "为用户表格添加一个新增按钮"
AI: "为产品表格添加一个导出按钮"
AI: "为订单表格添加一个自定义的发送邮件按钮"
```

**预定义操作类型：**
- `create` - 新增按钮
- `filter` - 筛选按钮
- `bulkDelete` - 批量删除按钮
- `export` - 导出按钮
- `import` - 导入按钮
- `refresh` - 刷新按钮
- `customRequest` - 自定义请求按钮

### 2. remove_table_action - 删除表格操作按钮

删除指定的表格操作按钮。

**AI 使用示例：**
```
AI: "删除表格中的导出按钮"
AI: "移除用户表格的批量删除功能"
```

### 3. update_table_action - 更新表格操作按钮

更新现有操作按钮的配置。

**AI 使用示例：**
```
AI: "将新增按钮的标题改为'添加用户'"
AI: "修改导出按钮的图标为下载图标"
```

### 4. list_table_actions - 列出表格操作按钮

查看表格中所有的操作按钮。

**AI 使用示例：**
```
AI: "显示用户表格中的所有操作按钮"
AI: "列出当前表格的操作功能"
```

### 5. configure_table_column - 配置表格列

为表格添加或配置列的显示。

**AI 使用示例：**
```
AI: "为用户表格添加一个邮箱列，宽度200px，可排序"
AI: "添加一个状态列，使用标签组件显示"
AI: "配置创建时间列，固定在左侧"
```

### 6. configure_table_filter - 配置表格筛选器

设置表格的筛选功能。

**AI 使用示例：**
```
AI: "为用户表格设置默认只显示活跃用户"
AI: "启用快速筛选功能，支持按姓名和邮箱搜索"
AI: "添加状态筛选器"
```

### 7. configure_table_sort - 配置表格排序

设置表格的排序规则。

**AI 使用示例：**
```
AI: "设置用户表格默认按创建时间倒序排列"
AI: "启用拖拽排序功能"
AI: "禁用表格的排序功能"
```

### 8. send_custom_request - 发送自定义请求

从表格上下文发送自定义请求。

**AI 使用示例：**
```
AI: "发送导出用户数据的请求"
AI: "执行批量更新用户状态的操作"
```

## 💡 实际使用场景

### 场景 1：快速设置用户管理表格

```
AI 对话：
用户: "帮我设置一个完整的用户管理表格，包括新增、编辑、删除、导出功能"

AI 会依次调用：
1. add_table_action (actionType: "create")
2. add_table_action (actionType: "bulkDelete") 
3. add_table_action (actionType: "export")
4. configure_table_column (配置用户名列)
5. configure_table_column (配置邮箱列)
6. configure_table_filter (启用快速筛选)
```

### 场景 2：自定义业务操作

```
AI 对话：
用户: "为订单表格添加一个'发送确认邮件'的按钮"

AI 会调用：
add_table_action (actionType: "custom", customConfig: {
  title: "发送确认邮件",
  action: "sendConfirmEmail",
  icon: "MailOutlined",
  requiresACL: true
})
```

### 场景 3：表格优化

```
AI 对话：
用户: "优化产品表格的显示，添加价格列，设置按销量排序，启用分类筛选"

AI 会调用：
1. configure_table_column (fieldName: "price")
2. configure_table_sort (defaultSort: ["-sales"])
3. configure_table_filter (quickFilterFields: ["category"])
```

## 🔧 高级配置

### 自定义操作按钮配置

```javascript
{
  title: "{{t('Custom Action')}}",
  action: "customAction",
  icon: "CustomIcon",
  type: "primary",
  align: "right",
  requiresACL: true,
  aclAction: "customPermission",
  componentProps: {
    confirm: {
      title: "确认操作",
      content: "是否执行此操作？"
    }
  }
}
```

### 表格列配置选项

```javascript
{
  title: "列标题",
  width: 200,
  fixed: "left", // 或 "right"
  sortable: true,
  filterable: true,
  component: "Input.Email", // 或其他组件
  componentProps: {
    // 组件特定属性
  }
}
```

### 筛选器配置

```javascript
{
  defaultFilter: {
    status: "active",
    type: { $in: ["user", "admin"] }
  },
  enableQuickFilter: true,
  quickFilterFields: ["name", "email", "phone"]
}
```

## 🚨 注意事项

1. **表格 UID 获取**：使用前需要先获取目标表格的 UID
2. **权限控制**：某些操作需要相应的 ACL 权限
3. **字段存在性**：配置列时确保字段在集合中存在
4. **API 限制**：遵循 NocoBase API 的调用限制
5. **错误处理**：工具会返回详细的错误信息

## 🔍 调试和故障排除

### 常见错误

1. **表格 UID 不存在**
   - 错误：`Schema not found`
   - 解决：检查表格 UID 是否正确

2. **权限不足**
   - 错误：`Permission denied`
   - 解决：检查用户权限和 ACL 配置

3. **字段不存在**
   - 错误：`Field not found`
   - 解决：确保字段在集合中已定义

### 调试工具

```bash
# 运行测试脚本
node scripts/verify-mcp-tools.js

# 查看详细日志
DEBUG=* npm start
```

## 📚 相关文档

- [SWAGGER_API_ANALYSIS.md](./SWAGGER_API_ANALYSIS.md) - API 分析文档
- [TABLE_OPERATION_TOOLS.md](./TABLE_OPERATION_TOOLS.md) - 工具详细说明
- [IMPLEMENTATION_SUMMARY.md](./IMPLEMENTATION_SUMMARY.md) - 实现总结

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这些工具！

---

**更新日期**: 2025-01-09  
**版本**: v1.0.0  
**适用于**: NocoBase MCP Server
