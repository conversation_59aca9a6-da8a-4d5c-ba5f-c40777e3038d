# NocoBase 表格操作 MCP 工具集

## 🎯 项目概述

基于 [SWAGGER_API_ANALYSIS.md](./SWAGGER_API_ANALYSIS.md) 的深入分析，我们为 NocoBase MCP 服务器成功实现了完整的表格操作工具集。这些工具允许 AI 客户端通过 MCP 协议直接操作 NocoBase 表格的各个方面。

## ✅ 实现状态

### 已完成功能

- ✅ **8 个 MCP 工具**：完整的表格操作工具集
- ✅ **7 种预定义模板**：常用操作的快速配置
- ✅ **API 客户端扩展**：底层 API 方法支持
- ✅ **完整文档**：使用指南和 API 分析
- ✅ **测试验证**：功能测试和集成验证
- ✅ **构建成功**：TypeScript 编译通过

### 表格级别工具

| 工具名称 | 功能描述 | 状态 |
|---------|----------|------|
| `add_table_action` | 添加表格操作按钮 | ✅ |
| `remove_table_action` | 删除表格操作按钮 | ✅ |
| `update_table_action` | 更新表格操作按钮 | ✅ |
| `list_table_actions` | 列出表格操作按钮 | ✅ |
| `configure_table_column` | 配置表格列 | ✅ |
| `configure_table_filter` | 配置表格筛选器 | ✅ |
| `configure_table_sort` | 配置表格排序 | ✅ |
| `send_custom_request` | 发送自定义请求 | ✅ |

### 行级别工具

| 工具名称 | 功能描述 | 状态 |
|---------|----------|------|
| `add_row_actions_column` | 添加表格行操作列 | ✅ |
| `add_row_action` | 添加单个行操作按钮 | ✅ |
| `create_record_advanced` | 高级记录创建 | ✅ |
| `update_record_advanced` | 高级记录更新 | ✅ |
| `association_operation` | 关联操作 | ✅ |
| `first_or_create` | 查找或创建 | ✅ |
| `update_or_create` | 更新或创建 | ✅ |

## 📚 文档结构

### 核心文档

- **[SWAGGER_API_ANALYSIS.md](./SWAGGER_API_ANALYSIS.md)** - NocoBase API 深度分析
- **[TABLE_OPERATION_TOOLS.md](./TABLE_OPERATION_TOOLS.md)** - 表格级别操作工具说明
- **[ROW_OPERATIONS_GUIDE.md](./ROW_OPERATIONS_GUIDE.md)** - 行级别操作工具指南
- **[MCP_USAGE_GUIDE.md](./MCP_USAGE_GUIDE.md)** - MCP 使用指南
- **[IMPLEMENTATION_SUMMARY.md](./IMPLEMENTATION_SUMMARY.md)** - 实现总结
- 完整示例
- 最佳实践

## 核心发现

### 1. 架构特点
- **模块化设计**: 每个功能都是独立的组件，便于维护和扩展
- **权限控制**: 完善的 ACL 机制，支持细粒度权限控制
- **数据驱动**: 基于 Schema 的配置方式，支持动态配置
- **性能优化**: 虚拟滚动、数据缓存等优化手段

### 2. 关键组件
- **TableBlock**: 表格块组件
- **TableV2**: 表格渲染引擎
- **SchemaInitializer**: 操作初始化器
- **ACLActionProvider**: 权限控制提供者

### 3. 扩展机制
- 自定义操作注册
- 自定义字段类型
- 自定义权限控制
- 自定义样式和主题

## 使用建议

1. **新手入门**: 先阅读 [API 参考](./API_REFERENCE.md) 了解基本概念
2. **深入理解**: 阅读 [源码分析](./SOURCE_CODE_ANALYSIS.md) 理解实现原理
3. **实践应用**: 参考 [使用示例](./API_USAGE_EXAMPLES.md) 进行开发

## 技术栈

- **前端框架**: React + TypeScript
- **UI 组件**: Ant Design
- **状态管理**: Formily
- **国际化**: i18next
- **构建工具**: Webpack

## 相关链接

- [NocoBase 官方文档](https://docs.nocobase.com/)
- [源码仓库](https://github.com/nocobase/nocobase)
- [社区论坛](https://github.com/nocobase/nocobase/discussions)

## 贡献指南

如果您发现文档中的错误或有改进建议，请提交 Issue 或 Pull Request。

---

*本文档基于 NocoBase 源码分析，版本信息请参考具体代码库。*