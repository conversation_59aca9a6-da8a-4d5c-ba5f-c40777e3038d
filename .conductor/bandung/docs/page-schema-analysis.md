# 📊 NocoBase 页面 Schema 结构深度分析

## 🎯 概述

通过 API 调用 `GET /api/uiSchemas:getJsonSchema/{schemaUid}` 和 `GET /api/uiSchemas:getProperties/{schemaUid}`，我们可以获取页面的完整区块排布结构。

## 📋 API 端点

### 1. 获取基本 Schema
```
GET /api/uiSchemas:getJsonSchema/{schemaUid}
```

**响应示例**：
```json
{
  "data": {
    "type": "void",
    "x-component": "Page",
    "name": "3h1qs93wgi4",
    "x-uid": "cafh7yoyd6w",
    "x-async": false
  }
}
```

### 2. 获取完整 Schema 属性
```
GET /api/uiSchemas:getProperties/{schemaUid}
```

这个 API 返回页面的完整结构，包括所有嵌套的区块和组件。

## 🏗️ Schema 结构层次

### 页面级别 (Page Level)
```
Page (cafh7yoyd6w)
└── Grid (9alxeyyy4q4) - 页面网格容器
    └── Grid.Row (6k4aomr3vvn) - 网格行
        ├── Grid.Col (3oxrn10ma4j) - 网格列 (width: 50)
        │   └── Markdown.Void (pqfddw8e41p) - Markdown 区块
        └── Grid.Col (mqmcu1wkd0m) - 网格列 (width: 50)
            ├── Iframe (wj6zgmbt9w3) - Iframe 区块
            └── ChartCardItem (7q3ynu2iqfs) - 图表区块
```

### 区块级别 (Block Level)

#### 1. Markdown 区块
```json
{
  "x-uid": "2pr61vx0dev",
  "type": "void",
  "x-settings": "blockSettings:markdown",
  "x-decorator": "CardItem",
  "x-component": "Markdown.Void",
  "x-component-props": {
    "content": "# 🎯 MCP 测试区块\n\n这是通过 **NocoBase MCP 服务器** 创建的 Markdown 区块！"
  }
}
```

#### 2. Table 区块
```json
{
  "type": "void",
  "x-decorator": "TableBlockProvider",
  "x-acl-action": "users:list",
  "x-decorator-props": {
    "collection": "users",
    "dataSource": "main",
    "action": "list",
    "params": {
      "pageSize": 20
    }
  },
  "x-component": "CardItem"
}
```

## 🔧 关键属性说明

### 组件标识
- **x-uid**: 组件的唯一标识符
- **x-component**: 组件类型 (Page, Grid, CardItem, TableV2, etc.)
- **x-decorator**: 装饰器类型 (TableBlockProvider, FormBlockProvider, etc.)

### 数据绑定
- **x-collection-field**: 绑定的集合字段
- **x-data-source**: 数据源标识
- **x-acl-action**: ACL 权限动作

### 配置属性
- **x-settings**: 设置菜单标识
- **x-initializer**: 初始化器标识
- **x-toolbar**: 工具栏标识

### 组件属性
- **x-component-props**: 组件属性配置
- **x-decorator-props**: 装饰器属性配置

## 🎭 Actions 列结构分析

从 Schema 中可以看到 Table Actions 列的完整结构：

```json
{
  "actions": {
    "type": "void",
    "title": "{{ t(\"Actions\") }}",
    "x-action-column": "actions",
    "x-decorator": "TableV2.Column.ActionBar",
    "x-component": "TableV2.Column",
    "x-initializer": "table:configureItemActions",
    "properties": {
      "escuzkxppb7": {
        "type": "void",
        "x-decorator": "DndContext",
        "x-component": "Space",
        "properties": {
          "iy1jofwnlx2": {
            "type": "void",
            "title": "{{ t(\"Popup\") }}",
            "x-action": "customize:popup",
            "x-component": "Action.Link"
          },
          "w2a9adyq1xf": {
            "type": "void",
            "title": "{{ t(\"View\") }}",
            "x-action": "view",
            "x-component": "Action.Link"
          },
          "9k70z9a7jzj": {
            "type": "void",
            "title": "{{ t(\"Edit\") }}",
            "x-action": "update",
            "x-component": "Action.Link"
          }
        }
      }
    }
  }
}
```

## 🚀 对 MCP 工具的指导意义

### 1. 区块发现
- 可以通过 Schema 分析发现页面上的所有区块
- 识别区块类型、配置和层次关系
- 了解区块的数据绑定和权限设置

### 2. 区块操作
- 通过 x-uid 精确定位和操作特定区块
- 理解区块的配置选项和可用操作
- 支持动态添加、修改、删除区块

### 3. 布局理解
- 理解 Grid 系统的行列结构
- 支持响应式布局配置
- 正确处理区块的嵌套关系

### 4. 数据流分析
- 理解数据源和集合的绑定关系
- 分析字段映射和显示配置
- 支持动态数据绑定

## 🎯 实际测试结果

通过 API 调用，我们成功获取了当前测试页面的完整结构：

### 页面基本信息
- **Schema UID**: `cafh7yoyd6w`
- **页面名称**: `3h1qs93wgi4`
- **组件类型**: `Page`

### 完整的区块层次结构

```
📄 Page (cafh7yoyd6w)
└── 🏗️ Grid (9alxeyyy4q4) - 页面主网格
    ├── 📏 Grid.Row (6k4aomr3vvn) - 第一行
    │   ├── 📐 Grid.Col (3oxrn10ma4j) - 左列 (width: 50)
    │   │   └── 📝 Markdown.Void (2pr61vx0dev) - MCP 测试区块
    │   └── 📐 Grid.Col (mqmcu1wkd0m) - 右列 (width: 50)
    │       ├── 🖼️ Iframe (4fvn5ccofs9) - Iframe 区块
    │       └── 📈 ChartCardItem (ryhguv8at6q) - 图表区块
    ├── 📏 Grid.Row (wn0puqj449o) - 第二行
    │   └── 📐 Grid.Col (tq87hscwqui) - 全宽列
    │       └── 📊 TableV2 (kfsvpy789ij) - Users 表格
    │           ├── 🎛️ ActionBar (jkwumcrvv9s) - 表格操作栏
    │           │   └── 🔘 Action (62m05wpapq0) - "Add new" 按钮
    │           │       └── 弹窗容器 (7ppmrn9y2z3)
    │           │           └── 📑 Tabs (n38eoqex593)
    │           │               └── 📄 Tab "Add new" (ao7rcxfanl2)
    │           │                   └── 🏗️ Grid (16fxybcl7h1)
    │           │                       └── 📝 FormV2 (1rkogbjgczg) - 创建表单
    │           │                           ├── 🏷️ username 字段
    │           │                           └── 🏷️ email 字段
    │           └── 📋 Actions Column (0aqquhcjx5x) - 行操作列
    │               ├── 🔗 Popup Action (omj2s3vbl15)
    │               ├── 🔗 View Action (gmho6d0nsq9)
    │               └── 🔗 Edit Action (c5okuk2dqbj)
    └── 📏 Grid.Row (noieofjfoch) - 空行
```

### 关键发现

1. **完整的层次结构**: API 返回了页面的完整嵌套结构，包括所有区块和子组件
2. **精确的 UID 映射**: 每个组件都有唯一的 UID，可以精确定位和操作
3. **详细的配置信息**: 包含组件属性、装饰器属性、数据绑定等完整信息
4. **Actions 系统**: 可以看到完整的 Actions 配置，包括弹窗、表单等嵌套结构

### MCP 工具的实现策略

基于这些发现，我们的 MCP 工具可以：

1. **页面分析**: 通过 `getSchemaProperties` API 获取页面的完整结构
2. **区块定位**: 使用 UID 精确定位特定的区块或组件
3. **智能操作**: 理解区块的类型和配置，提供相应的操作选项
4. **结构维护**: 在添加、修改、删除区块时保持正确的层次结构

这些发现为我们的 MCP 工具提供了强大的页面分析和操作能力！
