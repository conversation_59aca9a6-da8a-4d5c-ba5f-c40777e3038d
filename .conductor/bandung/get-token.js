#!/usr/bin/env node

/**
 * 使用 Playwright 登录并获取 token
 */

import { chromium } from 'playwright';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function main() {
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    console.log('🔐 登录 NocoBase 获取认证令牌...\n');
    
    // 访问登录页面
    await page.goto('http://localhost:13000/apps/mcp_playground/admin/signin');
    
    // 等待页面加载
    await page.waitForSelector('input[type="email"], input[type="text"], input[name="email"], input[name="account"]');
    
    // 填写登录表单
    await page.fill('input[type="email"], input[type="text"], input[name="email"], input[name="account"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'neo@123');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 等待登录完成
    await page.waitForURL('**/admin');
    
    console.log('✅ 登录成功！');
    
    // 获取 localStorage 中的 token
    const token = await page.evaluate(() => {
      return localStorage.getItem('NOCOBASE_TOKEN');
    });
    
    if (token) {
      console.log('\n🔑 认证令牌:');
      console.log(token);
      
      // 更新 check-menu-structure.js 中的 token
      
      const scriptPath = path.join(__dirname, 'check-menu-structure.js');
      let scriptContent = fs.readFileSync(scriptPath, 'utf8');
      
      // 替换 token
      const tokenRegex = /token: ['"`][^'"`]+['"`]/;
      scriptContent = scriptContent.replace(tokenRegex, `token: '${token}'`);
      
      fs.writeFileSync(scriptPath, scriptContent);
      console.log('\n✅ 已更新 check-menu-structure.js 中的认证令牌');
    } else {
      console.log('\n❌ 未找到认证令牌');
    }
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    await browser.close();
  }
}

main();