#!/usr/bin/env node

// 测试 MCP 工具的删除集合功能
import { spawn } from 'child_process';
import { once } from 'events';

async function testDeleteCollection() {
  console.log('=== 测试删除集合功能 ===\n');
  
  const mcpProcess = spawn('node', [
    'dist/index.js',
    '--base-url', 'http://app.dev.orb.local/api',
    '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs',
    '--app', 'mcp_playground'
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  let requestId = 0;
  
  async function sendRequest(method, params = {}) {
    const request = {
      jsonrpc: "2.0",
      id: ++requestId,
      method,
      params
    };
    
    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    
    const [data] = await once(mcpProcess.stdout, 'data');
    return JSON.parse(data.toString());
  }
  
  try {
    // 1. 初始化
    console.log('1. 初始化 MCP 服务器...');
    const initResponse = await sendRequest('initialize', {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    });
    
    if (initResponse.result) {
      console.log('✅ 初始化成功');
    }
    
    // 2. 先创建一个测试集合
    console.log('\n2. 创建测试集合（待删除）...');
    const createResponse = await sendRequest('tools/call', {
      name: 'create_collection',
      arguments: {
        name: 'test_delete_collection',
        title: 'Test Delete Collection',
        description: 'A collection created for testing deletion functionality',
        fields: [
          {
            name: 'name',
            type: 'string',
            interface: 'input',
            description: 'Item name',
            required: true
          },
          {
            name: 'value',
            type: 'integer',
            interface: 'number',
            description: 'Item value'
          }
        ]
      }
    });
    
    let collectionName = 'test_delete_collection';
    if (createResponse.result && createResponse.result.content) {
      const resultText = createResponse.result.content[0].text;
      console.log('创建响应:', resultText);
      
      // 尝试提取集合名
      const nameMatch = resultText.match(/name["\s]*:?["\s]*([a-zA-Z0-9_]+)/);
      if (nameMatch) {
        collectionName = nameMatch[1];
        console.log(`✅ 测试集合创建成功: ${collectionName}`);
      }
    }
    
    // 3. 验证集合存在
    console.log('\n3. 验证集合存在...');
    const listResponse = await sendRequest('tools/call', {
      name: 'list_collections',
      arguments: {}
    });
    
    if (listResponse.result && listResponse.result.content) {
      const resultText = listResponse.result.content[0].text;
      if (resultText.includes(collectionName)) {
        console.log('✅ 集合存在，可以删除');
      }
    }
    
    // 4. 删除集合
    console.log('\n4. 删除集合...');
    const deleteResponse = await sendRequest('tools/call', {
      name: 'delete_collection',
      arguments: {
        name: collectionName,
        confirm: true
      }
    });
    
    if (deleteResponse.result && deleteResponse.result.content) {
      const resultText = deleteResponse.result.content[0].text;
      console.log('✅ 集合删除成功:');
      console.log('响应:', resultText);
    } else {
      console.error('❌ 删除集合失败');
      if (deleteResponse.error) {
        console.error('错误:', deleteResponse.error.message);
      }
    }
    
    // 5. 验证集合已被删除
    console.log('\n5. 验证集合已被删除...');
    const verifyResponse = await sendRequest('tools/call', {
      name: 'list_collections',
      arguments: {}
    });
    
    if (verifyResponse.result && verifyResponse.result.content) {
      const resultText = verifyResponse.result.content[0].text;
      if (!resultText.includes(collectionName)) {
        console.log('✅ 确认集合已被删除');
      } else {
        console.log('⚠️ 集合可能仍然存在');
      }
    }
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  } finally {
    mcpProcess.kill();
  }
}

testDeleteCollection();