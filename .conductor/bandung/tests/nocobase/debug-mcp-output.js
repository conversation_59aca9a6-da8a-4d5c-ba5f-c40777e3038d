#!/usr/bin/env node

// 调试 MCP 输出格式
import { spawn } from 'child_process';
import { once } from 'events';

async function debugMCP() {
  console.log('=== 调试 MCP 输出 ===\n');
  
  const mcpProcess = spawn('node', [
    'dist/index.js',
    '--base-url', 'http://app.dev.orb.local/api',
    '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs',
    '--app', 'mcp_playground'
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  // 监听所有输出
  mcpProcess.stdout.on('data', (data) => {
    console.log('STDOUT:', data.toString());
  });
  
  mcpProcess.stderr.on('data', (data) => {
    console.log('STDERR:', data.toString());
  });
  
  let requestId = 0;
  
  // 发送请求
  async function sendRequest(method, params = {}) {
    const request = {
      jsonrpc: "2.0",
      id: ++requestId,
      method,
      params
    };
    
    console.log('\n发送请求:', JSON.stringify(request, null, 2));
    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
  }
  
  try {
    // 等待启动
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 初始化
    await sendRequest('initialize', {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    });
    
    // 等待响应
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 列出工具
    await sendRequest('tools/list');
    
    // 等待响应
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 调用工具
    await sendRequest('tools/call', {
      name: 'list_collections',
      arguments: { includeMeta: true }
    });
    
    // 等待响应
    await new Promise(resolve => setTimeout(resolve, 3000));
    
  } catch (error) {
    console.error('错误:', error);
  } finally {
    mcpProcess.kill();
  }
}

debugMCP();