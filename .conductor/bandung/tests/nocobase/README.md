# NocoBase MCP 测试

本目录包含用于测试 NocoBase MCP Collections 服务器的各种测试脚本。

## 测试脚本说明

### MCP 工具测试脚本

1. **test-mcp-comprehensive.js** - 综合测试脚本
   - 测试所有 MCP 工具功能
   - 验证集合、字段、记录的完整生命周期
   - 包含创建、查询、更新、删除操作
   - 运行命令: `node tests/nocobase/test-mcp-comprehensive.js`

2. **test-mcp-tools-improved.js** - MCP 工具调用测试（改进版）
   - 测试基本的 tools/call 功能
   - 验证创建记录和列出记录
   - 运行命令: `node tests/nocobase/test-mcp-tools-improved.js`

3. **test-mcp-update-record.js** - 更新记录测试
   - 专门测试记录更新功能
   - 包含创建、更新、验证流程
   - 运行命令: `node tests/nocobase/test-mcp-update-record.js`

4. **test-mcp-delete-record.js** - 删除记录测试
   - 测试记录删除功能
   - 包含创建、删除、验证流程
   - 运行命令: `node tests/nocobase/test-mcp-delete-record.js`

5. **test-mcp-delete-collection.js** - 删除集合测试
   - 测试集合删除功能
   - 包含创建、删除、验证流程
   - 运行命令: `node tests/nocobase/test-mcp-delete-collection.js`

### 客户端直接测试脚本

6. **view-collections-detail.js** - 查看集合详情
   - 直接使用 NocoBaseClient 查看所有集合和字段
   - 显示记录统计信息
   - 运行命令: `node tests/nocobase/view-collections-detail.js`

7. **list-collections-simple.js** - 简单集合列表
   - 使用客户端直接列出集合
   - 按类别分组显示
   - 运行命令: `node tests/nocobase/list-collections-simple.js`

8. **list-collections-mcp.js** - MCP 方式列出集合
   - 使用 MCP 协议列出集合
   - 运行命令: `node tests/nocobase/list-collections-mcp.js`

9. **test-collections-env.js** - 多环境测试
   - 测试不同环境的连接
   - 自动选择可用的环境
   - 运行命令: `node tests/nocobase/test-collections-env.js`

### 调试工具

10. **debug-mcp-output.js** - MCP 输出调试
    - 用于调试 MCP 协议通信
    - 显示原始请求和响应
    - 运行命令: `node tests/nocobase/debug-mcp-output.js`

## 测试环境配置

测试脚本使用以下环境配置：

- **Base URL**: `http://app.dev.orb.local/api`
- **App ID**: `mcp_playground`
- **Token**: 使用环境变量或硬编码的测试 token

## 运行测试

### 启动 MCP 服务器

```bash
# 使用 start.sh 脚本启动
./start.sh

# 或直接运行
node dist/index.js --base-url http://app.dev.orb.local/api --token <your-token> --app mcp_playground
```

### 运行单个测试

```bash
# 运行综合测试
node tests/nocobase/test-mcp-comprehensive.js

# 运行特定功能测试
node tests/nocobase/test-mcp-update-record.js
```

### 运行所有测试

```bash
# 在项目根目录运行
npm test
```

## 测试结果

所有测试都应显示 ✅ 表示成功。如果遇到错误，请检查：

1. MCP 服务器是否正在运行
2. 网络连接是否正常
3. 认证 token 是否有效
4. NocoBase 实例是否可访问

## 已验证的功能

✅ MCP 协议通信（initialize, tools/list, tools/call）
✅ 集合管理（创建、查询、更新、删除）
✅ 字段管理（列出、创建、更新、删除）
✅ 记录管理（CRUD 操作）
✅ 分类管理
✅ 分页和过滤
✅ 错误处理
✅ 模式分析

## 注意事项

1. 测试脚本会创建和删除测试数据，建议在测试环境中运行
2. 某些测试需要清理权限（删除集合和记录）
3. 如果测试失败，请查看错误信息并检查环境配置