#!/usr/bin/env node

// 简化版：直接使用 NocoBaseClient 查看集合
import { NocoBaseClient } from './dist/client.js';

async function listCollections() {
  console.log('=== 查看数据库集合 ===\n');
  
  try {
    const client = new NocoBaseClient({
      baseUrl: 'https://n.astra.xin/apps/mcp_playground',
      app: 'mcp_playground',
      token: 'neo@123'
    });
    
    console.log('正在获取集合列表...\n');
    
    const collections = await client.listCollections();
    
    console.log(`📊 找到 ${collections.length} 个数据库集合：\n`);
    
    // 按类别分组
    const categories = {};
    collections.forEach(collection => {
      const category = collection.category || '未分类';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(collection);
    });
    
    // 显示每个类别的集合
    for (const [category, items] of Object.entries(categories)) {
      console.log(`📁 ${category}:`);
      items.forEach(collection => {
        console.log(`   - ${collection.name} (${collection.title})`);
        if (collection.description) {
          console.log(`     描述: ${collection.description}`);
        }
        if (collection.fields) {
          console.log(`     字段数: ${collection.fields.length}`);
        }
        console.log('');
      });
    }
    
    // 显示集合统计
    console.log('\n📈 集合统计:');
    console.log(`- 总集合数: ${collections.length}`);
    console.log(`- 类别数: ${Object.keys(categories).length}`);
    
    // 显示系统集合和用户集合
    const systemCollections = collections.filter(c => 
      c.name.startsWith('system_') || 
      c.name.startsWith('pm_') ||
      c.name.startsWith('aj_') ||
      c.name.startsWith('ui_')
    );
    const userCollections = collections.filter(c => !systemCollections.includes(c));
    
    console.log(`- 系统集合: ${systemCollections.length}`);
    console.log(`- 用户集合: ${userCollections.length}`);
    
    if (userCollections.length > 0) {
      console.log('\n👤 用户创建的集合:');
      userCollections.forEach(collection => {
        console.log(`   - ${collection.name} (${collection.title})`);
      });
    }
    
  } catch (error) {
    console.error('❌ 获取失败:', error.message);
    if (error.response) {
      console.error(`状态码: ${error.response.status}`);
      if (error.response.data) {
        console.error(`错误详情: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }
  }
}

listCollections();