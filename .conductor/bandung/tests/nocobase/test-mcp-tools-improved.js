#!/usr/bin/env node

// 改进版：正确解析 MCP 工具返回的格式
import { spawn } from 'child_process';
import { once } from 'events';

async function testToolsCallImproved() {
  console.log('=== 使用 tools/call 方式测试（改进版）===\n');
  
  const mcpProcess = spawn('node', [
    'dist/index.js',
    '--base-url', 'http://app.dev.orb.local/api',
    '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs',
    '--app', 'mcp_playground'
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  let requestId = 0;
  
  // 发送请求并等待响应
  async function sendRequest(method, params = {}) {
    const request = {
      jsonrpc: "2.0",
      id: ++requestId,
      method,
      params
    };
    
    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    
    const [data] = await once(mcpProcess.stdout, 'data');
    return JSON.parse(data.toString());
  }
  
  try {
    // 1. 初始化
    console.log('1. 初始化 MCP 服务器...');
    const initResponse = await sendRequest('initialize', {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    });
    
    if (initResponse.result) {
      console.log('✅ 初始化成功');
    }
    
    // 2. 测试创建记录
    console.log('\n2. 测试创建记录...');
    const createRecordResponse = await sendRequest('tools/call', {
      name: 'create_record',
      arguments: {
        collection: 'test_products',
        data: {
          name: 'MCP Tools Test Product',
          price: 199.99,
          description: 'Created via MCP tools/call'
        }
      }
    });
    
    if (createRecordResponse.result && createRecordResponse.result.content) {
      const resultText = createRecordResponse.result.content[0].text;
      console.log('✅ 记录创建成功');
      console.log('响应:', resultText);
      
      // 尝试解析 JSON（如果有的话）
      try {
        const result = JSON.parse(resultText);
        if (result.id) {
          console.log(`记录 ID: ${result.id}`);
        }
      } catch (e) {
        // 不是 JSON，直接显示文本
      }
    } else {
      console.error('❌ 创建记录失败');
    }
    
    // 3. 测试列出记录
    console.log('\n3. 测试列出记录...');
    const listRecordsResponse = await sendRequest('tools/call', {
      name: 'list_records',
      arguments: {
        collection: 'test_products',
        pageSize: 5
      }
    });
    
    if (listRecordsResponse.result && listRecordsResponse.result.content) {
      const resultText = listRecordsResponse.result.content[0].text;
      console.log('✅ 记录列表获取成功');
      
      // 尝试解析 JSON
      try {
        const result = JSON.parse(resultText);
        if (result.data && Array.isArray(result.data)) {
          console.log(`\n找到 ${result.data.length} 条记录:`);
          result.data.forEach((record, index) => {
            console.log(`${index + 1}. ${record.name} - ¥${record.price}`);
          });
        }
      } catch (e) {
        console.log('响应:', resultText);
      }
    } else {
      console.error('❌ 获取记录失败');
    }
    
    // 4. 测试创建集合
    console.log('\n4. 测试创建集合...');
    const createCollectionResponse = await sendRequest('tools/call', {
      name: 'create_collection',
      arguments: {
        name: 'mcp_tools_test',
        title: 'MCP Tools Test',
        description: 'A collection created via MCP tools/call method',
        category: ['Test'],
        fields: [
          {
            name: 'title',
            type: 'string',
            interface: 'input',
            description: 'Test title',
            required: true
          },
          {
            name: 'status',
            type: 'string',
            interface: 'select',
            description: 'Test status'
          }
        ]
      }
    });
    
    if (createCollectionResponse.result && createCollectionResponse.result.content) {
      const resultText = createCollectionResponse.result.content[0].text;
      console.log('✅ 集合创建响应:');
      console.log(resultText);
    } else {
      console.error('❌ 创建集合失败');
    }
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  } finally {
    mcpProcess.kill();
  }
}

testToolsCallImproved();