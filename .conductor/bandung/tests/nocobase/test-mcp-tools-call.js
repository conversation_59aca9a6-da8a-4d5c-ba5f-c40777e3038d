#!/usr/bin/env node

// 使用 MCP tools/call 方式测试
import { spawn } from 'child_process';
import { once } from 'events';

async function testToolsCall() {
  console.log('=== 使用 tools/call 方式测试 ===\n');
  
  // 启动 MCP 服务器进程
  const mcpProcess = spawn('node', [
    'dist/index.js',
    '--base-url', 'http://app.dev.orb.local/api',
    '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs',
    '--app', 'mcp_playground'
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  let requestId = 0;
  
  // 发送请求并等待响应
  async function sendRequest(method, params = {}) {
    const request = {
      jsonrpc: "2.0",
      id: ++requestId,
      method,
      params
    };
    
    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    
    const [data] = await once(mcpProcess.stdout, 'data');
    return JSON.parse(data.toString());
  }
  
  try {
    // 1. 初始化
    console.log('1. 初始化 MCP 服务器...');
    const initResponse = await sendRequest('initialize', {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    });
    
    if (initResponse.result) {
      console.log('✅ 初始化成功');
    } else {
      throw new Error('初始化失败');
    }
    
    // 2. 列出工具
    console.log('\n2. 获取可用工具...');
    const toolsResponse = await sendRequest('tools/list');
    const tools = toolsResponse.result.tools;
    console.log(`✅ 找到 ${tools.length} 个工具`);
    
    // 3. 测试 list_collections 工具
    console.log('\n3. 测试 list_collections 工具...');
    const collectionsResponse = await sendRequest('tools/call', {
      name: 'list_collections',
      arguments: {
        includeMeta: true
      }
    });
    
    if (collectionsResponse.result && collectionsResponse.result.content) {
      const collections = JSON.parse(collectionsResponse.result.content[0].text);
      console.log(`✅ 成功获取 ${collections.length} 个集合:`);
      collections.forEach((collection, index) => {
        console.log(`   ${index + 1}. ${collection.name} (${collection.title})`);
      });
    } else {
      console.error('❌ 获取集合失败');
    }
    
    // 4. 测试创建集合
    console.log('\n4. 测试创建新集合...');
    const createResponse = await sendRequest('tools/call', {
      name: 'create_collection',
      arguments: {
        name: 'test_tools_collection',
        title: 'Test Tools Collection',
        description: 'A collection created via tools/call to test MCP functionality',
        category: ['Test'],
        fields: [
          {
            name: 'name',
            type: 'string',
            interface: 'input',
            description: 'Item name',
            required: true
          },
          {
            name: 'value',
            type: 'integer',
            interface: 'number',
            description: 'Item value'
          },
          {
            name: 'description',
            type: 'text',
            interface: 'textarea',
            description: 'Item description'
          }
        ]
      }
    });
    
    if (createResponse.result && createResponse.result.content) {
      const result = JSON.parse(createResponse.result.content[0].text);
      console.log('✅ 集合创建成功:', result.name);
    } else {
      console.error('❌ 创建集合失败');
      if (createResponse.error) {
        console.error('错误:', createResponse.error.message);
      }
    }
    
    // 5. 测试创建记录
    console.log('\n5. 测试创建记录...');
    const createRecordResponse = await sendRequest('tools/call', {
      name: 'create_record',
      arguments: {
        collection: 'test_products',
        data: {
          name: 'Test Product via MCP',
          price: 99.99,
          description: 'A product created using MCP tools/call'
        }
      }
    });
    
    if (createRecordResponse.result && createRecordResponse.result.content) {
      const record = JSON.parse(createRecordResponse.result.content[0].text);
      console.log('✅ 记录创建成功:');
      console.log(`   ID: ${record.id}`);
      console.log(`   名称: ${record.name}`);
      console.log(`   价格: ${record.price}`);
    } else {
      console.error('❌ 创建记录失败');
      if (createRecordResponse.error) {
        console.error('错误:', createRecordResponse.error.message);
      }
    }
    
    // 6. 测试获取记录
    console.log('\n6. 测试获取记录列表...');
    const listRecordsResponse = await sendRequest('tools/call', {
      name: 'list_records',
      arguments: {
        collection: 'test_products',
        pageSize: 10
      }
    });
    
    if (listRecordsResponse.result && listRecordsResponse.result.content) {
      const result = JSON.parse(listRecordsResponse.result.content[0].text);
      console.log(`✅ 成功获取记录列表:`);
      console.log(`   总数: ${result.meta.total}`);
      console.log(`   当前页: ${result.meta.page}`);
      console.log(`   每页: ${result.meta.pageSize}`);
      
      result.data.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.name} - ¥${record.price}`);
      });
    } else {
      console.error('❌ 获取记录失败');
    }
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    console.error(error.stack);
  } finally {
    // 清理进程
    mcpProcess.kill();
  }
}

testToolsCall();