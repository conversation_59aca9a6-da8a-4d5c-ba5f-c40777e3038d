#!/usr/bin/env node

// 测试 MCP 工具的更新记录功能
import { spawn } from 'child_process';
import { once } from 'events';

async function testUpdateRecord() {
  console.log('=== 测试更新记录功能 ===\n');
  
  const mcpProcess = spawn('node', [
    'dist/index.js',
    '--base-url', 'http://app.dev.orb.local/api',
    '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs',
    '--app', 'mcp_playground'
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  let requestId = 0;
  
  async function sendRequest(method, params = {}) {
    const request = {
      jsonrpc: "2.0",
      id: ++requestId,
      method,
      params
    };
    
    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    
    const [data] = await once(mcpProcess.stdout, 'data');
    return JSON.parse(data.toString());
  }
  
  try {
    // 1. 初始化
    console.log('1. 初始化 MCP 服务器...');
    const initResponse = await sendRequest('initialize', {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    });
    
    if (initResponse.result) {
      console.log('✅ 初始化成功');
    }
    
    // 2. 先创建一个测试记录
    console.log('\n2. 创建测试记录...');
    const createResponse = await sendRequest('tools/call', {
      name: 'create_record',
      arguments: {
        collection: 'test_products',
        data: {
          name: 'Product to Update',
          price: 99.99,
          description: 'This product will be updated'
        }
      }
    });
    
    let recordId;
    if (createResponse.result && createResponse.result.content) {
      const resultText = createResponse.result.content[0].text;
      console.log('创建响应:', resultText);
      
      // 尝试从文本中提取 ID
      const idMatch = resultText.match(/ID:\s*(\d+)/);
      if (idMatch) {
        recordId = parseInt(idMatch[1]);
        console.log(`✅ 测试记录创建成功，ID: ${recordId}`);
      } else {
        console.log('⚠️ 无法从响应中提取记录ID');
        return;
      }
    }
    
    // 3. 更新记录
    console.log('\n3. 更新记录...');
    const updateResponse = await sendRequest('tools/call', {
      name: 'update_record',
      arguments: {
        collection: 'test_products',
        id: recordId,
        data: {
          name: 'Updated Product Name',
          price: 149.99,
          description: 'This product has been updated via MCP'
        }
      }
    });
    
    if (updateResponse.result && updateResponse.result.content) {
      const resultText = updateResponse.result.content[0].text;
      console.log('✅ 记录更新成功:');
      console.log('响应:', resultText);
      
      // 尝试解析 JSON
      try {
        const result = JSON.parse(resultText);
        if (result.id) {
          console.log(`   ID: ${result.id}`);
          console.log(`   新名称: ${result.name}`);
          console.log(`   新价格: ${result.price}`);
          console.log(`   新描述: ${result.description}`);
        }
      } catch (e) {
        // 不是 JSON，已经显示了文本
      }
    } else {
      console.error('❌ 更新记录失败');
      if (updateResponse.error) {
        console.error('错误:', updateResponse.error.message);
      }
    }
    
    // 4. 验证更新
    console.log('\n4. 验证更新结果...');
    const getResponse = await sendRequest('tools/call', {
      name: 'get_record',
      arguments: {
        collection: 'test_products',
        id: recordId
      }
    });
    
    if (getResponse.result && getResponse.result.content) {
      const resultText = getResponse.result.content[0].text;
      console.log('✅ 验证成功，记录已更新:');
      console.log('响应:', resultText);
      
      // 尝试解析 JSON
      try {
        const record = JSON.parse(resultText);
        if (record.id) {
          console.log(`   名称: ${record.name}`);
          console.log(`   价格: ${record.price}`);
          console.log(`   描述: ${record.description}`);
        }
      } catch (e) {
        // 不是 JSON，已经显示了文本
      }
    }
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  } finally {
    mcpProcess.kill();
  }
}

testUpdateRecord();