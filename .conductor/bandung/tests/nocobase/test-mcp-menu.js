#!/usr/bin/env node

// 测试 MCP 工具查看菜单结构
import { spawn } from 'child_process';
import { once } from 'events';

async function testMCPTools() {
  console.log('=== 测试 MCP 工具查看菜单结构 ===\n');
  
  try {
    // 启动 MCP 服务器
    const mcpProcess = spawn('node', [
      'dist/index.js',
      '--base-url', 'https://n.astra.xin/apps/mcp_playground',
      '--token', 'neo@123',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    // 等待进程启动
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 发送初始化请求
    const initRequest = {
      jsonrpc: "2.0",
      id: 1,
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: {},
        clientInfo: {
          name: "test-client",
          version: "1.0.0"
        }
      }
    };
    
    mcpProcess.stdin.write(JSON.stringify(initRequest) + '\n');
    
    // 读取初始化响应
    const [initData] = await once(mcpProcess.stdout, 'data');
    console.log('初始化响应:', initData.toString());
    
    // 发送工具列表请求
    const toolsRequest = {
      jsonrpc: "2.0",
      id: 2,
      method: "tools/list"
    };
    
    mcpProcess.stdin.write(JSON.stringify(toolsRequest) + '\n');
    
    // 读取工具列表响应
    const [toolsData] = await once(mcpProcess.stdout, 'data');
    console.log('\n可用工具:', toolsData.toString());
    
    // 查找菜单相关的工具
    const toolsResponse = JSON.parse(toolsData.toString());
    const menuTools = toolsResponse.result.tools.filter(tool => 
      tool.name.toLowerCase().includes('route') || 
      tool.name.toLowerCase().includes('menu') ||
      tool.name.toLowerCase().includes('page')
    );
    
    console.log('\n菜单相关工具:');
    menuTools.forEach(tool => {
      console.log(`- ${tool.name}: ${tool.description}`);
    });
    
    // 如果找到路由工具，调用它
    if (menuTools.length > 0) {
      const routeTool = menuTools.find(t => t.name.includes('route'));
      if (routeTool) {
        console.log(`\n调用工具 ${routeTool.name}...`);
        
        const toolCall = {
          jsonrpc: "2.0",
          id: 3,
          method: "tools/call",
          params: {
            name: routeTool.name,
            arguments: {
              tree: true
            }
          }
        };
        
        mcpProcess.stdin.write(JSON.stringify(toolCall) + '\n');
        
        // 读取工具调用响应
        const [toolData] = await once(mcpProcess.stdout, 'data');
        console.log('\n菜单结构:');
        console.log(toolData.toString());
      }
    }
    
    // 清理
    mcpProcess.kill();
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testMCPTools();