#!/usr/bin/env node

// 详细查看数据库集合和字段信息
import { NocoBaseClient } from './dist/client.js';

async function viewCollections() {
  console.log('=== NocoBase 数据库集合详情 ===\n');
  
  const client = new NocoBaseClient({
    baseUrl: 'http://app.dev.orb.local/api',
    app: 'mcp_playground',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs'
  });
  
  try {
    const collections = await client.listCollections();
    
    console.log(`📊 总共有 ${collections.length} 个集合\n`);
    
    for (const collection of collections) {
      console.log(`📁 ${collection.name} (${collection.title})`);
      console.log(`   ID: ${collection.key}`);
      if (collection.description) {
        console.log(`   描述: ${collection.description}`);
      }
      
      // 获取字段信息
      try {
        const fields = await client.listFields(collection.name);
        console.log(`   字段 (${fields.length} 个):`);
        
        fields.forEach(field => {
          const required = field.required ? ' (必填)' : '';
          const unique = field.unique ? ' (唯一)' : '';
          console.log(`     - ${field.name}: ${field.type}${required}${unique}`);
          if (field.description) {
            console.log(`       描述: ${field.description}`);
          }
        });
      } catch (error) {
        console.log(`   ⚠️  无法获取字段信息: ${error.message}`);
      }
      
      console.log('');
    }
    
    // 获取记录统计
    console.log('\n📈 记录统计:');
    for (const collection of collections) {
      try {
        const records = await client.listRecords(collection.name, { pageSize: 1 });
        console.log(`   ${collection.name}: ${records.meta?.total || 0} 条记录`);
      } catch (error) {
        console.log(`   ${collection.name}: 无法获取记录数`);
      }
    }
    
  } catch (error) {
    console.error('❌ 获取失败:', error.message);
  }
}

viewCollections();