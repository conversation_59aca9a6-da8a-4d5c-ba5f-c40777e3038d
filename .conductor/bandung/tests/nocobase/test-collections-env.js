#!/usr/bin/env node

// 使用可用的环境测试查看集合
import { NocoBaseClient } from './dist/client.js';

async function listCollections() {
  console.log('=== 测试查看数据库集合 ===\n');
  
  // 测试不同的环境配置
  const configs = [
    {
      name: 'Orb Local (HTTP)',
      baseUrl: 'http://app.dev.orb.local/api',
      app: 'mcp_playground',
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs'
    },
    {
      name: 'NocoBase Docker',
      baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
      app: 'mcp_playground',
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
    }
  ];
  
  for (const config of configs) {
    console.log(`\n🔍 测试环境: ${config.name}`);
    console.log(`URL: ${config.baseUrl}`);
    console.log('-' * 50);
    
    try {
      const client = new NocoBaseClient({
        baseUrl: config.baseUrl,
        app: config.app,
        token: config.token
      });
      
      const collections = await client.listCollections();
      
      console.log(`✅ 成功！找到 ${collections.length} 个集合`);
      
      // 显示前10个集合
      console.log('\n前10个集合:');
      collections.slice(0, 10).forEach((collection, index) => {
        console.log(`${index + 1}. ${collection.name} (${collection.title})`);
      });
      
      if (collections.length > 10) {
        console.log(`\n... 还有 ${collections.length - 10} 个集合`);
      }
      
      // 统计用户创建的集合
      const userCollections = collections.filter(c => 
        !c.name.startsWith('system_') && 
        !c.name.startsWith('pm_') &&
        !c.name.startsWith('aj_') &&
        !c.name.startsWith('ui_')
      );
      
      if (userCollections.length > 0) {
        console.log(`\n👤 用户创建的集合 (${userCollections.length} 个):`);
        userCollections.forEach(collection => {
          console.log(`   - ${collection.name}: ${collection.title}`);
        });
      }
      
      break; // 如果成功，停止测试其他环境
      
    } catch (error) {
      console.error(`❌ ${config.name} 失败: ${error.message}`);
      if (error.response) {
        console.error(`   状态码: ${error.response.status}`);
      }
    }
  }
}

listCollections();