#!/usr/bin/env node

// 使用 MCP 工具查看数据库集合
import { spawn } from 'child_process';
import { once } from 'events';

async function listCollections() {
  console.log('=== 使用 MCP 工具查看数据库集合 ===\n');
  
  try {
    // 启动 MCP 服务器
    const mcpProcess = spawn('node', [
      'dist/index.js',
      '--base-url', 'https://n.astra.xin/apps/mcp_playground',
      '--token', 'neo@123',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    // 等待进程启动
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 发送初始化请求
    const initRequest = {
      jsonrpc: "2.0",
      id: 1,
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: {},
        clientInfo: {
          name: "test-client",
          version: "1.0.0"
        }
      }
    };
    
    mcpProcess.stdin.write(JSON.stringify(initRequest) + '\n');
    
    // 读取初始化响应
    const [initData] = await once(mcpProcess.stdout, 'data');
    const initResponse = JSON.parse(initData.toString());
    
    if (initResponse.result) {
      console.log('✅ MCP 服务器初始化成功');
    } else {
      throw new Error('初始化失败');
    }
    
    // 发送列出集合请求
    const listRequest = {
      jsonrpc: "2.0",
      id: 2,
      method: "tools/call",
      params: {
        name: "list_collections",
        arguments: {
          includeMeta: true
        }
      }
    };
    
    mcpProcess.stdin.write(JSON.stringify(listRequest) + '\n');
    
    // 读取集合列表响应
    const [listData] = await once(mcpProcess.stdout, 'data');
    const listResponse = JSON.parse(listData.toString());
    
    if (listResponse.result && listResponse.result.content) {
      const collections = JSON.parse(listResponse.result.content[0].text);
      
      console.log(`\n📊 找到 ${collections.length} 个数据库集合：\n`);
      
      // 按类别分组
      const categories = {};
      collections.forEach(collection => {
        const category = collection.category || '未分类';
        if (!categories[category]) {
          categories[category] = [];
        }
        categories[category].push(collection);
      });
      
      // 显示每个类别的集合
      for (const [category, items] of Object.entries(categories)) {
        console.log(`📁 ${category}:`);
        items.forEach(collection => {
          console.log(`   - ${collection.name} (${collection.title})`);
          if (collection.description) {
            console.log(`     描述: ${collection.description}`);
          }
          console.log(`     字段数: ${collection.fields?.length || 0}`);
          console.log('');
        });
      }
      
      // 显示集合统计
      console.log('\n📈 集合统计:');
      console.log(`- 总集合数: ${collections.length}`);
      console.log(`- 类别数: ${Object.keys(categories).length}`);
      
      // 显示系统集合和用户集合
      const systemCollections = collections.filter(c => c.name.startsWith('system_') || c.name.startsWith('pm_'));
      const userCollections = collections.filter(c => !systemCollections.includes(c));
      
      console.log(`- 系统集合: ${systemCollections.length}`);
      console.log(`- 用户集合: ${userCollections.length}`);
      
    } else {
      console.error('❌ 获取集合列表失败');
      if (listResponse.error) {
        console.error(`错误: ${listResponse.error.message}`);
      }
    }
    
    // 清理
    mcpProcess.kill();
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  }
}

listCollections();