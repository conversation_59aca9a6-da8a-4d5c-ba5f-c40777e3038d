#!/usr/bin/env node

// 测试 MCP 工具的删除记录功能
import { spawn } from 'child_process';
import { once } from 'events';

async function testDeleteRecord() {
  console.log('=== 测试删除记录功能 ===\n');
  
  const mcpProcess = spawn('node', [
    'dist/index.js',
    '--base-url', 'http://app.dev.orb.local/api',
    '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs',
    '--app', 'mcp_playground'
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  let requestId = 0;
  
  async function sendRequest(method, params = {}) {
    const request = {
      jsonrpc: "2.0",
      id: ++requestId,
      method,
      params
    };
    
    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    
    const [data] = await once(mcpProcess.stdout, 'data');
    return JSON.parse(data.toString());
  }
  
  try {
    // 1. 初始化
    console.log('1. 初始化 MCP 服务器...');
    const initResponse = await sendRequest('initialize', {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    });
    
    if (initResponse.result) {
      console.log('✅ 初始化成功');
    }
    
    // 2. 先创建一个测试记录
    console.log('\n2. 创建测试记录（待删除）...');
    const createResponse = await sendRequest('tools/call', {
      name: 'create_record',
      arguments: {
        collection: 'test_products',
        data: {
          name: 'Product to Delete',
          price: 299.99,
          description: 'This product will be deleted'
        }
      }
    });
    
    let recordId;
    if (createResponse.result && createResponse.result.content) {
      const resultText = createResponse.result.content[0].text;
      console.log('创建响应:', resultText);
      
      // 提取 ID
      const idMatch = resultText.match(/ID:\s*(\d+)/);
      if (idMatch) {
        recordId = parseInt(idMatch[1]);
        console.log(`✅ 测试记录创建成功，ID: ${recordId}`);
      }
    }
    
    // 3. 验证记录存在
    console.log('\n3. 验证记录存在...');
    const getResponse = await sendRequest('tools/call', {
      name: 'get_record',
      arguments: {
        collection: 'test_products',
        id: recordId
      }
    });
    
    if (getResponse.result && getResponse.result.content) {
      console.log('✅ 记录存在，可以删除');
    }
    
    // 4. 删除记录
    console.log('\n4. 删除记录...');
    const deleteResponse = await sendRequest('tools/call', {
      name: 'delete_record',
      arguments: {
        collection: 'test_products',
        id: recordId,
        confirm: true
      }
    });
    
    if (deleteResponse.result && deleteResponse.result.content) {
      const resultText = deleteResponse.result.content[0].text;
      console.log('✅ 记录删除成功:');
      console.log('响应:', resultText);
    } else {
      console.error('❌ 删除记录失败');
      if (deleteResponse.error) {
        console.error('错误:', deleteResponse.error.message);
      }
    }
    
    // 5. 验证记录已被删除
    console.log('\n5. 验证记录已被删除...');
    try {
      const verifyResponse = await sendRequest('tools/call', {
        name: 'get_record',
        arguments: {
          collection: 'test_products',
          id: recordId
        }
      });
      
      if (verifyResponse.error) {
        console.log('✅ 确认记录已被删除（获取时返回错误）');
      }
    } catch (error) {
      console.log('✅ 确认记录已被删除（请求失败）');
    }
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  } finally {
    mcpProcess.kill();
  }
}

testDeleteRecord();