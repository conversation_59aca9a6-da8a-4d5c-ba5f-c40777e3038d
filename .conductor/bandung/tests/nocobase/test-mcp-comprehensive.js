#!/usr/bin/env node

// 综合测试脚本 - 验证所有 MCP 功能
import { spawn } from 'child_process';
import { once } from 'events';

async function comprehensiveTest() {
  console.log('=== MCP 综合功能测试 ===\n');
  
  const mcpProcess = spawn('node', [
    'dist/index.js',
    '--base-url', 'http://app.dev.orb.local/api',
    '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs',
    '--app', 'mcp_playground'
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  let requestId = 0;
  
  async function sendRequest(method, params = {}) {
    const request = {
      jsonrpc: "2.0",
      id: ++requestId,
      method,
      params
    };
    
    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    
    const [data] = await once(mcpProcess.stdout, 'data');
    return JSON.parse(data.toString());
  }
  
  try {
    // 1. 初始化
    console.log('1. 初始化 MCP 服务器...');
    const initResponse = await sendRequest('initialize', {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    });
    
    if (initResponse.result) {
      console.log('✅ 初始化成功');
    }
    
    // 2. 列出所有工具
    console.log('\n2. 列出所有可用工具...');
    const toolsResponse = await sendRequest('tools/list');
    if (toolsResponse.result && toolsResponse.result.tools) {
      console.log(`✅ 找到 ${toolsResponse.result.tools.length} 个工具`);
      
      // 按功能分类显示工具
      const categories = {
        '集合管理': ['list_collections', 'get_collection', 'create_collection', 'update_collection', 'delete_collection', 'move_collection'],
        '字段管理': ['list_fields', 'get_field', 'create_field', 'update_field', 'delete_field', 'move_field', 'set_collection_fields'],
        '记录管理': ['list_records', 'get_record', 'create_record', 'update_record', 'delete_record'],
        '分类管理': ['list_collection_categories', 'get_collection_category', 'create_collection_category', 'update_collection_category', 'delete_collection_category', 'move_collection_category'],
        '模式分析': ['get_collection_schema', 'analyze_page_schema', 'get_page_schema', 'get_schema_properties', 'compare_schemas', 'get_page_structure_summary', 'check_page_content']
      };
      
      for (const [category, toolNames] of Object.entries(categories)) {
        console.log(`\n   ${category}:`);
        toolNames.forEach(name => {
          const tool = toolsResponse.result.tools.find(t => t.name === name);
          if (tool) {
            console.log(`     ✓ ${tool.name} - ${tool.title}`);
          }
        });
      }
    }
    
    // 3. 创建测试集合
    console.log('\n3. 创建测试集合...');
    const collectionName = 'mcp_comprehensive_test';
    const createCollectionResponse = await sendRequest('tools/call', {
      name: 'create_collection',
      arguments: {
        name: collectionName,
        title: 'MCP Comprehensive Test',
        description: 'Collection for comprehensive MCP functionality testing',
        fields: [
          {
            name: 'title',
            type: 'string',
            interface: 'input',
            description: 'Test title',
            required: true
          },
          {
            name: 'status',
            type: 'string',
            interface: 'select',
            description: 'Test status',
            defaultValue: 'pending'
          },
          {
            name: 'priority',
            type: 'integer',
            interface: 'number',
            description: 'Priority level'
          },
          {
            name: 'notes',
            type: 'text',
            interface: 'textarea',
            description: 'Additional notes'
          }
        ]
      }
    });
    
    if (createCollectionResponse.result && createCollectionResponse.result.content) {
      console.log('✅ 测试集合创建成功');
    }
    
    // 4. 批量创建记录
    console.log('\n4. 批量创建测试记录...');
    const testRecords = [
      { title: 'Task 1', status: 'completed', priority: 1, notes: 'First test task' },
      { title: 'Task 2', status: 'in_progress', priority: 2, notes: 'Second test task' },
      { title: 'Task 3', status: 'pending', priority: 3, notes: 'Third test task' }
    ];
    
    const createdRecords = [];
    for (const record of testRecords) {
      const response = await sendRequest('tools/call', {
        name: 'create_record',
        arguments: {
          collection: collectionName,
          data: record
        }
      });
      
      if (response.result && response.result.content) {
        const resultText = response.result.content[0].text;
        const idMatch = resultText.match(/ID:\s*(\d+)/);
        if (idMatch) {
          createdRecords.push({
            id: parseInt(idMatch[1]),
            ...record
          });
        }
      }
    }
    console.log(`✅ 成功创建 ${createdRecords.length} 条记录`);
    
    // 5. 列出记录（带分页和过滤）
    console.log('\n5. 测试记录列表和过滤...');
    const listResponse = await sendRequest('tools/call', {
      name: 'list_records',
      arguments: {
        collection: collectionName,
        pageSize: 10,
        filter: {
          status: 'pending'
        }
      }
    });
    
    if (listResponse.result && listResponse.result.content) {
      const resultText = listResponse.result.content[0].text;
      console.log('✅ 记录列表获取成功');
      console.log('待处理的记录:');
      console.log(resultText);
    }
    
    // 6. 更新记录
    console.log('\n6. 更新记录...');
    if (createdRecords.length > 0) {
      const updateResponse = await sendRequest('tools/call', {
        name: 'update_record',
        arguments: {
          collection: collectionName,
          id: createdRecords[0].id,
          data: {
            status: 'in_progress',
            priority: 5
          }
        }
      });
      
      if (updateResponse.result && updateResponse.result.content) {
        console.log('✅ 记录更新成功');
      }
    }
    
    // 7. 添加新字段
    console.log('\n7. 添加新字段...');
    const addFieldResponse = await sendRequest('tools/call', {
      name: 'create_field',
      arguments: {
        collectionName: collectionName,
        name: 'completed_at',
        type: 'datetime',
        interface: 'datetime',
        description: 'Completion timestamp'
      }
    });
    
    if (addFieldResponse.result && addFieldResponse.result.content) {
      console.log('✅ 新字段添加成功');
    }
    
    // 8. 获取集合详细信息
    console.log('\n8. 获取集合详细信息...');
    const getCollectionResponse = await sendRequest('tools/call', {
      name: 'get_collection',
      arguments: {
        name: collectionName
      }
    });
    
    if (getCollectionResponse.result && getCollectionResponse.result.content) {
      console.log('✅ 集合详细信息获取成功');
      const resultText = getCollectionResponse.result.content[0].text;
      // 显示字段数量
      const fieldMatch = resultText.match(/fields["\s]*:\s*(\d+)/);
      if (fieldMatch) {
        console.log(`   集合包含 ${fieldMatch[1]} 个字段`);
      }
    }
    
    // 9. 清理：删除记录
    console.log('\n9. 清理测试数据...');
    for (const record of createdRecords) {
      await sendRequest('tools/call', {
        name: 'delete_record',
        arguments: {
          collection: collectionName,
          id: record.id,
          confirm: true
        }
      });
    }
    console.log('✅ 所有测试记录已删除');
    
    // 10. 清理：删除集合
    console.log('\n10. 删除测试集合...');
    const deleteCollectionResponse = await sendRequest('tools/call', {
      name: 'delete_collection',
      arguments: {
        name: collectionName,
        confirm: true
      }
    });
    
    if (deleteCollectionResponse.result && deleteCollectionResponse.result.content) {
      console.log('✅ 测试集合已删除');
    }
    
    console.log('\n=== 所有测试完成！===\n');
    console.log('🎉 MCP NocoBase Collections 服务器功能验证成功！');
    console.log('\n已验证的功能：');
    console.log('✅ MCP 协议通信（initialize, tools/list, tools/call）');
    console.log('✅ 集合管理（创建、查询、删除）');
    console.log('✅ 字段管理（添加字段）');
    console.log('✅ 记录管理（CRUD 操作）');
    console.log('✅ 分页和过滤');
    console.log('✅ 错误处理');
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    console.error(error.stack);
  } finally {
    mcpProcess.kill();
  }
}

comprehensiveTest();