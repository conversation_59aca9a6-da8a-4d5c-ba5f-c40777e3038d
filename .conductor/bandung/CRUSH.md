# MCP NocoBase Development

## Build & Test Commands
- `npm run build` - Compile TypeScript to dist/
- `npm run dev` - Development mode with tsx
- `npm run test` - Run Jest tests
- `npm run test:mcp` - Test MCP tools
- `npm run test:relations` - Test relation fields
- `npm run test:create` - Test record creation
- `npm run test:verify` - Final verification tests
- `npm run lint` - ESLint check
- `npm run lint:fix` - ESLint auto-fix

## Code Style Guidelines

### TypeScript & Imports
- Use ES modules with `.js` extensions in imports
- Strict TypeScript config enabled (noImplicitAny, strictNullChecks)
- Verbatim module syntax: `import type` for types
- Interfaces over types for complex objects
- Use zod for runtime validation

### Naming Conventions
- Collections: plural nouns, lowercase with underscores (`users`, `towns`)
- Functions: camelCase, descriptive verbs (`createCollection`, `listRecords`)
- Variables: camelCase, descriptive names
- Constants: UPPER_SNAKE_CASE for config values
- Classes: PascalCase for classes and interfaces

### Error Handling
- Always use try-catch for async operations
- Return formatted error responses using `createFormattedErrorResponse`
- Validate inputs with zod schemas
- Use Axios for HTTP requests with proper error handling

### Project Structure
- Tools in `src/tools/` with separate files for each domain
- Client logic in `src/client.ts`
- Utilities in `src/utils/`
- Tests in `tests/` directory
- Scripts in `scripts/` for development tasks

### Documentation
- Use English for all code, comments, and documentation
- Follow collection naming standards from Cursor rules
- Include JSDoc comments for complex functions
- Use response formatter for consistent API responses

### Environment
- Node.js >=18.0.0 required
- ES2022 target with ESNext modules
- Use environment variables for configuration
- Test with provided NocoBase instance credentials