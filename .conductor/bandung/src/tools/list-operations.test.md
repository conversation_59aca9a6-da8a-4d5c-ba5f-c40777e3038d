# List Operations Tools Test Guide

This document provides test cases and examples for the List Block operations tools.

## Available Tools

### Data Operations
1. `get_list_data` - Get data for a list block with pagination, filtering, and sorting
2. `create_list_item` - Create a new item in a list block
3. `update_list_item` - Update an item in a list block
4. `delete_list_item` - Delete an item from a list block
5. `bulk_delete_list_items` - Delete multiple items from a list block
6. `view_list_item` - View details of a specific item in a list block

### List Management
7. `refresh_list` - Refresh data in a list block
8. `filter_list` - Apply filters to a list block
9. `add_list_action` - Add an action button to a list block
10. `configure_list_item_actions` - Configure action buttons for list items
11. `configure_list_fields` - Configure field display in a list block

### Advanced Operations
12. `export_list_data` - Export data from a list block
13. `import_list_data` - Import data to a list block collection
14. `custom_list_request` - Send a custom request for list block operations

## Test Cases

### 1. Get List Data
```json
{
  "tool": "get_list_data",
  "arguments": {
    "collectionName": "users",
    "page": 1,
    "pageSize": 10,
    "filter": {
      "status": "active"
    },
    "fields": ["id", "name", "email"],
    "appends": ["profile"],
    "sort": ["name", "-createdAt"]
  }
}
```

### 2. Create List Item
```json
{
  "tool": "create_list_item",
  "arguments": {
    "collectionName": "users",
    "values": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "status": "active"
    },
    "updateAssociationValues": true
  }
}
```

### 3. Update List Item
```json
{
  "tool": "update_list_item",
  "arguments": {
    "collectionName": "users",
    "itemId": 1,
    "values": {
      "name": "John Smith",
      "status": "inactive"
    }
  }
}
```

### 4. Delete List Item
```json
{
  "tool": "delete_list_item",
  "arguments": {
    "collectionName": "users",
    "itemId": 1
  }
}
```

### 5. Bulk Delete List Items
```json
{
  "tool": "bulk_delete_list_items",
  "arguments": {
    "collectionName": "users",
    "itemIds": [1, 2, 3]
  }
}
```

### 6. View List Item
```json
{
  "tool": "view_list_item",
  "arguments": {
    "collectionName": "users",
    "itemId": 1,
    "fields": ["id", "name", "email", "profile"],
    "appends": ["profile"]
  }
}
```

### 7. Add List Action
```json
{
  "tool": "add_list_action",
  "arguments": {
    "listUid": "list_uid_123",
    "actionType": "create",
    "position": "beforeEnd"
  }
}
```

### 8. Configure List Fields
```json
{
  "tool": "configure_list_fields",
  "arguments": {
    "listUid": "list_uid_123",
    "fields": [
      {
        "name": "name",
        "title": "Name",
        "component": "Input.ReadPretty",
        "span": 12
      },
      {
        "name": "email",
        "title": "Email",
        "component": "Input.ReadPretty",
        "span": 12
      }
    ]
  }
}
```

### 9. Configure List Item Actions
```json
{
  "tool": "configure_list_item_actions",
  "arguments": {
    "listUid": "list_uid_123",
    "actions": [
      {
        "actionType": "view"
      },
      {
        "actionType": "edit"
      },
      {
        "actionType": "delete"
      }
    ]
  }
}
```

### 10. Export List Data
```json
{
  "tool": "export_list_data",
  "arguments": {
    "collectionName": "users",
    "format": "xlsx",
    "fields": ["id", "name", "email"],
    "filter": {
      "status": "active"
    }
  }
}
```

### 11. Import List Data
```json
{
  "tool": "import_list_data",
  "arguments": {
    "collectionName": "users",
    "file": "/path/to/users.xlsx",
    "updateStrategy": "upsert",
    "fields": ["name", "email"]
  }
}
```

### 12. Custom List Request
```json
{
  "tool": "custom_list_request",
  "arguments": {
    "url": "/api/custom/list-endpoint",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json"
    },
    "data": {
      "customParam": "value"
    }
  }
}
```

## API Mapping

These tools map to the following NocoBase API endpoints:

- `get_list_data` → `GET /{collection}:list`
- `create_list_item` → `POST /{collection}:create`
- `update_list_item` → `POST /{collection}:update`
- `delete_list_item` → `POST /{collection}:destroy`
- `bulk_delete_list_items` → `POST /{collection}:destroy`
- `view_list_item` → `GET /{collection}:get`
- `export_list_data` → `POST /{collection}:export`
- `import_list_data` → `POST /{collection}:importXlsx`

## Schema Operations

List configuration tools work with NocoBase UI Schema:

- `add_list_action` → `POST /uiSchemas:insertAdjacent`
- `configure_list_fields` → `POST /uiSchemas:patch`
- `configure_list_item_actions` → `POST /uiSchemas:patch`

## Error Handling

All tools include comprehensive error handling and return structured responses:

```json
{
  "content": [
    {
      "type": "text",
      "text": "Operation completed successfully: {...}"
    }
  ]
}
```

Or for errors:

```json
{
  "content": [
    {
      "type": "text",
      "text": "Error: {error message}"
    }
  ]
}
```
