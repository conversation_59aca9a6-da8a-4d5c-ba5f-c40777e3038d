#!/usr/bin/env node

/**
 * 测试 MCP 工具与新的响应格式化器
 * 模拟真实的 MCP 工具调用，展示格式化效果
 */

import { NocoBaseClient } from '../dist/client.js';
import { createFormattedResponse, createFormattedErrorResponse } from '../dist/utils/response-formatter.js';

// 使用测试环境配置
const client = new NocoBaseClient({
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw',
  app: 'mcp_playground'
});

async function testMcpToolsWithFormatter() {
  console.log('🧪 测试 MCP 工具与响应格式化器集成\n');
  console.log('=' .repeat(80));

  try {
    // 测试 1: 获取集合列表
    console.log('\n📋 测试 1: 获取集合列表');
    console.log('-'.repeat(50));
    
    const collections = await client.listCollections();
    const collectionsResponse = createFormattedResponse(
      `Found ${collections.length} collections:`,
      collections,
      'collection'
    );
    
    console.log('✅ 格式化后的集合列表响应:');
    console.log(collectionsResponse.content[0].text);

    console.log('\n' + '='.repeat(80));

    // 测试 2: 获取页面 Schema
    console.log('\n🏗️  测试 2: 获取页面 Schema');
    console.log('-'.repeat(50));
    
    try {
      // 尝试获取一个页面的 Schema
      const routes = await client.listRoutes({ tree: false });
      if (routes.length > 0) {
        const firstRoute = routes[0];
        console.log(`尝试获取路由 "${firstRoute.path}" 的 Schema...`);
        
        if (firstRoute.schemaUid) {
          const schema = await client.getPageSchema(firstRoute.schemaUid);
          const schemaResponse = createFormattedResponse(
            `Page Schema for route "${firstRoute.path}":`,
            schema,
            'schema'
          );
          
          console.log('✅ 格式化后的 Schema 响应:');
          console.log(schemaResponse.content[0].text.substring(0, 2000) + '...\n[响应已截断]');
        } else {
          console.log('⚠️  该路由没有 schemaUid');
        }
      } else {
        console.log('⚠️  没有找到可用的路由');
      }
    } catch (error) {
      console.log('❌ Schema 获取失败:', error.message);
    }

    console.log('\n' + '='.repeat(80));

    // 测试 3: 获取列表数据
    console.log('\n📊 测试 3: 获取列表数据');
    console.log('-'.repeat(50));
    
    try {
      // 尝试获取第一个集合的数据
      if (collections.length > 0) {
        const firstCollection = collections[0];
        console.log(`尝试获取集合 "${firstCollection.name}" 的数据...`);
        
        const listData = await client.getListData(firstCollection.name, {
          page: 1,
          pageSize: 5
        });
        
        const listResponse = createFormattedResponse(
          `List data from collection "${firstCollection.name}":`,
          listData,
          'list'
        );
        
        console.log('✅ 格式化后的列表数据响应:');
        console.log(listResponse.content[0].text);
      } else {
        console.log('⚠️  没有可用的集合');
      }
    } catch (error) {
      console.log('❌ 列表数据获取失败:', error.message);
    }

    console.log('\n' + '='.repeat(80));

    // 测试 4: 错误处理
    console.log('\n❌ 测试 4: 错误处理');
    console.log('-'.repeat(50));
    
    try {
      await client.getListData('non_existent_collection', { page: 1, pageSize: 10 });
    } catch (error) {
      const errorResponse = createFormattedErrorResponse(error);
      console.log('✅ 格式化后的错误响应:');
      console.log(errorResponse.content[0].text);
      console.log('isError:', errorResponse.isError);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n✅ 所有测试完成！');
  console.log('\n📝 总结:');
  console.log('- ✅ 响应格式化器成功集成到 MCP 工具中');
  console.log('- ✅ 大数据自动触发压缩和分析');
  console.log('- ✅ 小数据保持原始格式');
  console.log('- ✅ 错误处理统一格式化');
  console.log('- ✅ YAML 格式提高可读性');
}

// 运行测试
testMcpToolsWithFormatter().catch(console.error);
