#!/usr/bin/env node

// 分析 page_1 的 tabs 子路由，看看真正的可编辑区域在哪里
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          // 分析 page_1 的 tabs 子路由的 schema
          const tabsSchemaUid = 'c7owgwgeww4'; // 从上面的输出中获取
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'analyze_page_schema', arguments: { schemaUid: tabsSchemaUid } } });
        } else if (msg.id === 2) {
          console.log('=== page_1 tabs 子路由 Schema 分析 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 获取原始 schema
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'get_page_schema', arguments: { schemaUid: 'c7owgwgeww4' } } });
        } else if (msg.id === 3) {
          console.log('\n=== page_1 tabs 子路由原始 Schema ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 尝试列出 tabs 的区块
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'list_page_blocks', arguments: { schemaUid: 'c7owgwgeww4' } } });
        } else if (msg.id === 4) {
          console.log('\n=== page_1 tabs 子路由区块列表 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'analyze-tabs', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
