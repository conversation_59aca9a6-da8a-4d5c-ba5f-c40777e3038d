#!/usr/bin/env node

// 测试新创建的正确页面是否能添加区块
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let correctPage = null;
  let tabsRoute = null;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          // 获取所有路由，找到我们刚创建的页面
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct_all', arguments: {} } });
        } else if (msg.id === 2) {
          let raw = ''; for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          const payload = JSON.parse(raw); const list = payload?.data || payload || [];
          
          // 找到正确的 MCP 页面
          function findRoutes(nodes = []) {
            for (const n of nodes) {
              if (n.type === 'page' && n.title && n.title.includes('正确的 MCP 页面')) {
                correctPage = n;
                // 找到它的 tab 子路由
                if (n.children && n.children.length > 0) {
                  tabsRoute = n.children.find(c => c.type === 'tab');
                }
              }
              if (Array.isArray(n.children)) findRoutes(n.children);
            }
          }
          findRoutes(list);
          
          if (!correctPage) { console.log('未找到正确的 MCP 页面'); server.kill(); return; }
          if (!tabsRoute) { console.log('未找到 tabs 子路由'); server.kill(); return; }
          
          console.log('找到正确页面:', correctPage.title);
          console.log('tabs 子路由 schemaUid:', tabsRoute.schemaUid);
          
          // 分析 tabs 子路由的 schema
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'analyze_page_schema', arguments: { schemaUid: tabsRoute.schemaUid } } });
        } else if (msg.id === 3) {
          console.log('\n=== tabs 子路由 Schema 分析 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 尝试向 tabs 子路由添加 Markdown 区块
          console.log('\n=== 尝试添加 Markdown 区块 ===');
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'add_markdown_block', arguments: { parentUid: tabsRoute.schemaUid, title: '测试 Markdown', content: '# 成功！\n\n这个区块是通过 MCP 添加到正确结构的页面中的。' } } });
        } else if (msg.id === 4) {
          console.log('Markdown 区块添加结果:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 列出区块验证
          send(server, { jsonrpc: '2.0', id: 5, method: 'tools/call', params: { name: 'list_page_blocks', arguments: { schemaUid: tabsRoute.schemaUid } } });
        } else if (msg.id === 5) {
          console.log('\n=== 区块列表验证 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'test-correct-page', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
