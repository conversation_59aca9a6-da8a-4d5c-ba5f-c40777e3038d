import axios from 'axios';
import https from 'https';
import testConfig from './test-config.js';

// 调试路由问题
async function debugRoutes() {
  console.log('🔍 调试路由问题\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    },
    httpsAgent: new https.Agent({
      rejectUnauthorized: false
    })
  });

  try {
    // 1. 查看当前所有路由的详细信息
    console.log('📋 1. 当前所有路由详细信息');
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    console.log(`✅ 找到 ${routes.length} 个路由:`);
    routes.forEach(route => {
      console.log(`\n   路由 ID: ${route.id}`);
      console.log(`   标题: "${route.title}"`);
      console.log(`   类型: ${route.type}`);
      console.log(`   Schema UID: ${route.schemaUid || 'null'}`);
      console.log(`   父级 ID: ${route.parentId || 'null'}`);
      console.log(`   排序: ${route.sort || 'null'}`);
      console.log(`   隐藏菜单: ${route.hideInMenu || false}`);
      console.log(`   启用标签: ${route.enableTabs || false}`);
      console.log(`   启用头部: ${route.enableHeader || false}`);
      if (route.options) {
        console.log(`   选项: ${JSON.stringify(route.options)}`);
      }
    });

    // 2. 查找有问题的路由（标题为 null 或 schemaUid 为 null）
    console.log('\n📋 2. 识别有问题的路由');
    const problematicRoutes = routes.filter(route => 
      route.title === 'null' || route.title === null || !route.schemaUid
    );
    
    console.log(`🚨 发现 ${problematicRoutes.length} 个有问题的路由:`);
    problematicRoutes.forEach(route => {
      console.log(`   - ID: ${route.id}, 标题: "${route.title}", Schema UID: ${route.schemaUid || 'null'}`);
    });

    // 3. 查看最近创建的 Student Management 路由
    console.log('\n📋 3. 查看 Student Management 路由');
    const studentRoute = routes.find(r => r.title === 'Student Management');
    if (studentRoute) {
      console.log('   找到 Student Management 路由:');
      console.log(`   - ID: ${studentRoute.id}`);
      console.log(`   - 标题: "${studentRoute.title}"`);
      console.log(`   - Schema UID: ${studentRoute.schemaUid || 'null'}`);
      console.log(`   - 类型: ${studentRoute.type}`);
      
      // 如果有 schemaUid，检查对应的 schema
      if (studentRoute.schemaUid) {
        try {
          const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${studentRoute.schemaUid}`);
          console.log('   - Schema 存在: ✅');
          console.log(`   - Schema 组件: ${schemaResponse.data.data['x-component'] || 'N/A'}`);
        } catch (error) {
          console.log('   - Schema 状态: ❌ 不存在或无法访问');
        }
      } else {
        console.log('   - Schema UID: ❌ 缺失');
      }
    } else {
      console.log('   ❌ 未找到 Student Management 路由');
    }

    // 4. 检查正常工作的路由示例
    console.log('\n📋 4. 检查正常工作的路由示例');
    const workingRoutes = routes.filter(route => 
      route.title !== 'null' && route.title !== null && route.schemaUid && route.type === 'page'
    );
    
    if (workingRoutes.length > 0) {
      const exampleRoute = workingRoutes[0];
      console.log('   正常路由示例:');
      console.log(`   - ID: ${exampleRoute.id}`);
      console.log(`   - 标题: "${exampleRoute.title}"`);
      console.log(`   - Schema UID: ${exampleRoute.schemaUid}`);
      console.log(`   - 类型: ${exampleRoute.type}`);
      console.log(`   - 启用标签: ${exampleRoute.enableTabs}`);
      console.log(`   - 启用头部: ${exampleRoute.enableHeader}`);
    }

    // 5. 尝试创建一个正确的路由
    console.log('\n📋 5. 尝试创建一个正确的测试路由');
    
    try {
      // 先创建 UI Schema
      const pageSchemaUid = `page-${Date.now()}-test`;
      const tabSchemaUid = `tab-${Date.now()}-test`;
      
      const pageSchema = {
        type: 'void',
        'x-component': 'Page',
        'x-app-version': '1.4.0-alpha',
        properties: {
          [tabSchemaUid]: {
            type: 'void',
            'x-component': 'Tabs',
            'x-initializer': 'page:addTab',
            'x-component-props': {},
            properties: {
              tab1: {
                type: 'void',
                title: '{{t("Untitled")}}',
                'x-component': 'Tabs.TabPane',
                'x-designer': 'Tabs.Designer',
                'x-component-props': {},
                properties: {
                  grid: {
                    type: 'void',
                    'x-component': 'Grid',
                    'x-initializer': 'page:addBlock',
                    properties: {}
                  }
                }
              }
            }
          }
        }
      };

      // 插入 UI Schema
      await client.post('/uiSchemas:insertAdjacent', {
        targetUid: 'root',
        position: 'beforeEnd',
        schema: pageSchema,
        'x-uid': pageSchemaUid
      });
      
      console.log(`   ✅ 创建 UI Schema 成功: ${pageSchemaUid}`);

      // 创建路由
      const correctRouteData = {
        title: 'Test Correct Route',
        type: 'page',
        schemaUid: pageSchemaUid,
        enableTabs: true,
        enableHeader: true
      };

      const routeResponse = await client.post('/desktopRoutes:create', correctRouteData);
      const newRoute = routeResponse.data.data;
      
      console.log('   ✅ 创建正确路由成功:');
      console.log(`   - ID: ${newRoute.id}`);
      console.log(`   - 标题: "${newRoute.title}"`);
      console.log(`   - Schema UID: ${newRoute.schemaUid}`);
      console.log(`   - 类型: ${newRoute.type}`);
      
    } catch (error) {
      console.log(`   ❌ 创建正确路由失败: ${error.response?.data?.message || error.message}`);
    }

    console.log('\n🎯 调试总结:');
    console.log('   1. 检查了所有路由的详细信息');
    console.log('   2. 识别了有问题的路由');
    console.log('   3. 分析了路由创建的正确方式');
    console.log('   4. 尝试创建了一个正确的测试路由');
    
    console.log('\n💡 问题分析:');
    console.log('   - 路由标题显示为 "null" 可能是因为 schemaUid 缺失或无效');
    console.log('   - 需要先创建有效的 UI Schema，然后再创建路由');
    console.log('   - 路由的 schemaUid 必须指向一个存在的 UI Schema');

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行调试
debugRoutes().catch(console.error);
