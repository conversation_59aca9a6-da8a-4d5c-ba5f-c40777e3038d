#!/usr/bin/env node

// 通过 MCP 调用 routes_dump_raw_direct，查看服务端原始响应
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

async function main() {
  const server = spawn('node', [
    'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app
  ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write(d.toString()));
  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          const callReq = { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct', arguments: {} } };
          server.stdin.write(JSON.stringify(callReq) + '\n');
        } else if (msg.id === 2) {
          console.log('\n===== routes_dump_raw_direct =====');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          console.log('===== End =====');
          server.kill();
        }
      } catch {}
    }
  });

  const initReq = { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'dump', version: '1.0.0' } } };
  server.stdin.write(JSON.stringify(initReq) + '\n');
}

main().catch((e) => { console.error(e); process.exit(1); });

