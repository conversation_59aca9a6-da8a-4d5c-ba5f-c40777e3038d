#!/usr/bin/env node

// 通过 MCP 协议调用 routes_tree_overview，打印当前路由树
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

async function main() {
  console.log('🚀 Starting MCP server with provided environment...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', config.baseUrl,
    '--token', config.token,
    '--app', config.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  server.stderr.on('data', (data) => {
    const msg = data.toString();
    process.stderr.write(`🔍 [server] ${msg}`);
  });

  let initialized = false;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter((l) => l.trim());
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          // initialize ok -> call routes_tree_overview
          const callReq = {
            jsonrpc: '2.0',
            id: 2,
            method: 'tools/call',
            params: {
              name: 'routes_tree_overview',
              arguments: {
                includeIds: true,
                includeHidden: true
              }
            }
          };
          server.stdin.write(JSON.stringify(callReq) + '\n');
        } else if (msg.id === 2) {
          // print overview
          const content = msg.result?.content || msg.error?.content || [];
          console.log('\n===== Routes Tree Overview =====');
          for (const item of content) {
            if (item?.type === 'text' && item?.text) {
              console.log(item.text);
            } else if (typeof item === 'string') {
              console.log(item);
            }
          }
          console.log('===== End =====');
          server.kill();
        }
      } catch (e) {
        // non-json line
        // ignore
      }
    }
  });

  // send initialize
  const initReq = {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: { tools: {} },
      clientInfo: { name: 'routes-overview-script', version: '1.0.0' }
    }
  };
  server.stdin.write(JSON.stringify(initReq) + '\n');
}

main().catch((err) => {
  console.error('❌ Failed:', err);
  process.exit(1);
});

