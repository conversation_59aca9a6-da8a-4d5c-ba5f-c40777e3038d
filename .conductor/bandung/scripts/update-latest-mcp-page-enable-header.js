#!/usr/bin/env node

import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let latestPageId = null;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct_all', arguments: {} } });
        } else if (msg.id === 2) {
          let raw = ''; for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          const payload = JSON.parse(raw); const list = payload?.data || payload || [];
          const pages = []; (function walk(nodes=[]) { for (const n of nodes) { if (n.type === 'page') pages.push(n); if (Array.isArray(n.children)) walk(n.children); } })(list);
          pages.sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
          if (!pages[0]) { console.log('未找到任何页面'); server.kill(); return; }
          latestPageId = pages[0].id;
          console.log('将更新最新页面启用标题栏/显示标题，ID=', latestPageId);
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'update_route', arguments: { id: latestPageId, enableHeader: true, displayTitle: true } } });
        } else if (msg.id === 3) {
          console.log('已更新最新页面标题栏设置');
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'get_route', arguments: { id: latestPageId } } });
        } else if (msg.id === 4) {
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'update-latest-page', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });

