#!/usr/bin/env node

/**
 * 测试表格操作工具
 */

import { NocoBaseClient } from '../dist/client.js';
import { 
  handleAddTableAction,
  handleListTableActions,
  handleConfigureTableColumn,
  handleConfigureTableFilter,
  handleConfigureTableSort,
  TABLE_ACTION_TEMPLATES
} from '../dist/tools/table-operations.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
});

async function testTableOperations() {
  console.log('🚀 开始测试表格操作工具...\n');

  try {
    // 1. 测试预定义操作模板
    console.log('📋 预定义操作模板：');
    Object.keys(TABLE_ACTION_TEMPLATES).forEach(key => {
      const template = TABLE_ACTION_TEMPLATES[key];
      console.log(`  - ${key}: ${template.title} (${template.action})`);
    });
    console.log();

    // 2. 测试获取页面 Schema（需要一个实际的页面 UID）
    console.log('🔍 获取页面 Schema...');
    try {
      // 这里需要一个实际存在的页面 UID
      const pageUid = 'test_page_uid'; // 替换为实际的页面 UID
      const schema = await client.getPageSchema(pageUid);
      console.log('✅ 页面 Schema 获取成功');
      
      // 查找表格区块
      function findTableBlocks(obj, path = '') {
        const tables = [];
        if (obj && typeof obj === 'object') {
          if (obj['x-component'] === 'TableBlockProvider' || obj['x-decorator'] === 'TableBlockProvider') {
            tables.push({
              uid: obj['x-uid'],
              path,
              collection: obj['x-decorator-props']?.collection || obj['x-component-props']?.collection
            });
          }
          
          if (obj.properties) {
            Object.keys(obj.properties).forEach(key => {
              tables.push(...findTableBlocks(obj.properties[key], path ? `${path}.${key}` : key));
            });
          }
        }
        return tables;
      }
      
      const tableBlocks = findTableBlocks(schema);
      console.log(`📊 找到 ${tableBlocks.length} 个表格区块：`);
      tableBlocks.forEach(table => {
        console.log(`  - UID: ${table.uid}, Collection: ${table.collection}, Path: ${table.path}`);
      });
      
      if (tableBlocks.length > 0) {
        const testTableUid = tableBlocks[0].uid;
        
        // 3. 测试列出表格操作
        console.log('\n📝 测试列出表格操作...');
        const listResult = await handleListTableActions(client, { tableUid: testTableUid });
        console.log('✅ 列出表格操作成功：');
        console.log(listResult.content[0].text);
        
        // 4. 测试添加表格操作按钮
        console.log('\n➕ 测试添加表格操作按钮...');
        const addResult = await handleAddTableAction(client, {
          tableUid: testTableUid,
          actionType: 'refresh'
        });
        console.log('✅ 添加表格操作成功：');
        console.log(addResult.content[0].text);
        
        // 5. 测试配置表格列
        console.log('\n🏗️ 测试配置表格列...');
        const columnResult = await handleConfigureTableColumn(client, {
          tableUid: testTableUid,
          fieldName: 'test_field',
          columnConfig: {
            title: '测试列',
            width: 150,
            sortable: true,
            component: 'Input'
          }
        });
        console.log('✅ 配置表格列成功：');
        console.log(columnResult.content[0].text);
        
        // 6. 测试配置表格筛选器
        console.log('\n🔍 测试配置表格筛选器...');
        const filterResult = await handleConfigureTableFilter(client, {
          tableUid: testTableUid,
          filterConfig: {
            enableQuickFilter: true,
            quickFilterFields: ['name', 'title']
          }
        });
        console.log('✅ 配置表格筛选器成功：');
        console.log(filterResult.content[0].text);
        
        // 7. 测试配置表格排序
        console.log('\n📊 测试配置表格排序...');
        const sortResult = await handleConfigureTableSort(client, {
          tableUid: testTableUid,
          sortConfig: {
            defaultSort: ['-createdAt'],
            enableDragSort: false
          }
        });
        console.log('✅ 配置表格排序成功：');
        console.log(sortResult.content[0].text);
      }
      
    } catch (error) {
      console.log('⚠️ 页面 Schema 测试跳过（需要实际的页面 UID）');
      console.log(`错误信息: ${error.message}`);
    }

    // 8. 测试 API 客户端扩展方法
    console.log('\n🔧 测试 API 客户端扩展方法...');
    
    // 测试 insertAdjacentSchema 方法
    console.log('测试 insertAdjacentSchema 方法存在性：', typeof client.insertAdjacentSchema === 'function');
    console.log('测试 patchSchema 方法存在性：', typeof client.patchSchema === 'function');
    console.log('测试 removeSchema 方法存在性：', typeof client.removeSchema === 'function');
    console.log('测试 addTableAction 方法存在性：', typeof client.addTableAction === 'function');
    console.log('测试 sendCustomRequest 方法存在性：', typeof client.sendCustomRequest === 'function');

    console.log('\n✅ 表格操作工具测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误：', error);
    console.error('错误详情：', error.stack);
  }
}

// 运行测试
testTableOperations().catch(console.error);
