#!/usr/bin/env node

/**
 * 测试 MCP Kanban 操作工具
 * 
 * 这个脚本测试新增的看板操作工具功能
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { NocoBaseClient } from '../dist/client.js';
import { registerKanbanOperationTools } from '../dist/tools/kanban-operations.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
});

async function testKanbanOperations() {
  console.log('🚀 测试 MCP Kanban 操作工具...\n');

  // 创建 MCP 服务器实例
  const server = new McpServer({
    name: "test-kanban-operations",
    version: "0.1.0",
  });

  // 注册看板操作工具
  await registerKanbanOperationTools(server, client);

  console.log('📋 Kanban 操作工具已注册到 MCP 服务器');
  console.log('新增工具列表：');
  const expectedTools = [
    'get_kanban_data',
    'create_kanban_card',
    'update_kanban_card',
    'delete_kanban_card',
    'move_kanban_card',
    'batch_update_kanban_cards',
    'add_kanban_action',
    'configure_kanban_filter',
    'configure_kanban_group_field',
    'configure_kanban_sort_field',
    'create_sort_field'
  ];
  expectedTools.forEach(tool => {
    console.log(`  ✅ ${tool}`);
  });
  console.log();

  // 测试基础API方法
  console.log('🧪 测试基础 API 方法...\n');

  try {
    // 1. 测试获取看板数据
    console.log('1️⃣ 测试获取看板数据...');
    const kanbanData = await client.getKanbanData('tasks', {
      groupField: 'status',
      sortField: 'priority_sort',
      fields: ['id', 'title', 'status']
    });
    console.log(`   ✅ 获取到 ${kanbanData?.length || 0} 条看板数据`);

    // 2. 测试创建看板卡片
    console.log('2️⃣ 测试创建看板卡片...');
    const newCard = await client.createKanbanCard('tasks', {
      title: 'MCP测试卡片',
      description: '通过MCP工具创建的测试卡片'
    }, 'status', 'todo');
    console.log(`   ✅ 创建卡片成功，ID: ${newCard?.id}`);

    if (newCard?.id) {
      // 3. 测试更新看板卡片
      console.log('3️⃣ 测试更新看板卡片...');
      await client.updateKanbanCard('tasks', newCard.id, {
        title: 'MCP测试卡片（已更新）',
        description: '通过MCP工具更新的测试卡片'
      });
      console.log(`   ✅ 更新卡片成功，ID: ${newCard.id}`);

      // 4. 测试移动看板卡片
      console.log('4️⃣ 测试移动看板卡片...');
      await client.moveKanbanCard('tasks', {
        sourceId: newCard.id,
        targetScope: { status: 'doing' },
        sortField: 'priority_sort'
      });
      console.log(`   ✅ 移动卡片成功，ID: ${newCard.id}`);

      // 5. 测试删除看板卡片
      console.log('5️⃣ 测试删除看板卡片...');
      await client.deleteKanbanCard('tasks', newCard.id);
      console.log(`   ✅ 删除卡片成功，ID: ${newCard.id}`);
    }

    // 6. 测试创建排序字段
    console.log('6️⃣ 测试创建排序字段...');
    try {
      const sortField = await client.createSortField('tasks', {
        name: 'test_sort',
        title: 'MCP测试排序字段'
      });
      console.log(`   ✅ 创建排序字段成功: ${sortField?.name}`);
    } catch (error) {
      console.log(`   ⚠️  排序字段可能已存在: ${error.message}`);
    }

  } catch (error) {
    console.error(`❌ API 测试失败: ${error.message}`);
  }

  console.log('\n🎉 Kanban 操作工具测试完成！');
  console.log('\n📚 使用说明：');
  console.log('这些工具现在可以通过 MCP 协议被 AI 客户端调用，提供完整的看板操作能力：');
  console.log('- 📊 数据获取：get_kanban_data');
  console.log('- 📝 卡片管理：create/update/delete_kanban_card');
  console.log('- 🔄 卡片移动：move_kanban_card');
  console.log('- 📦 批量操作：batch_update_kanban_cards');
  console.log('- ⚙️  配置管理：configure_kanban_*');
  console.log('- 🎛️  操作按钮：add_kanban_action');
  console.log('- 🔧 字段管理：create_sort_field');
}

// 运行测试
testKanbanOperations().catch(console.error);
