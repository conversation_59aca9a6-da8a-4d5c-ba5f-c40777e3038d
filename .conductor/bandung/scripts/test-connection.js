#!/usr/bin/env node

/**
 * 测试 NocoBase 连接
 */

import { NocoBaseClient } from '../dist/client.js';

async function testConnection() {
  console.log('🔗 Testing NocoBase connection...\n');
  
  const configs = [
    {
      name: 'Config 1 - Correct API URL',
      baseUrl: 'https://n.astra.xin/api',
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM',
      app: 'mcp_playground'
    }
  ];
  
  for (const config of configs) {
    console.log(`\n📋 Testing ${config.name}:`);
    console.log(`   URL: ${config.baseUrl}`);
    console.log(`   Token: ${config.token.substring(0, 20)}...`);
    console.log(`   App: ${config.app}`);

    try {
      const client = new NocoBaseClient(config);
      
      // 测试基本连接
      const collections = await client.listCollections();
      console.log(`   ✅ Connection successful`);
      console.log(`   📊 Found ${collections.data?.length || 0} collections`);
      
      if (collections.data && collections.data.length > 0) {
        console.log(`   📝 Sample collections: ${collections.data.slice(0, 3).map(c => c.name).join(', ')}`);
      }
      
      // 测试路由
      try {
        const routes = await client.listRoutes();
        console.log(`   🛣️  Found ${routes.data?.length || 0} routes`);
      } catch (routeError) {
        console.log(`   ⚠️  Routes test failed: ${routeError.message}`);
      }
      
      return config; // 返回成功的配置
      
    } catch (error) {
      console.log(`   ❌ Connection failed: ${error.message}`);
    }
  }
  
  return null;
}

// 运行测试
testConnection().then(successConfig => {
  if (successConfig) {
    console.log(`\n🎉 Found working configuration: ${successConfig.name}`);
    console.log('Use this configuration for further tests.');
  } else {
    console.log('\n💥 All connection attempts failed.');
    console.log('Please check:');
    console.log('1. NocoBase instance is running');
    console.log('2. URL is correct');
    console.log('3. Authentication credentials are valid');
    console.log('4. Network connectivity');
  }
}).catch(error => {
  console.error('💥 Test runner crashed:', error);
  process.exit(1);
});
