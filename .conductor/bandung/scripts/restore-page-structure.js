import axios from 'axios';
import testConfig from './test-config.js';

// 恢复页面的正确结构
async function restorePageStructure() {
  console.log('🔧 恢复页面的正确结构\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    const pageUid = 'page-1754235700602-luqwmsxu9';
    
    // 1. 获取当前页面状态
    console.log('📋 1. 获取当前页面状态');
    const currentSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
    const currentSchema = currentSchemaResponse.data.data;
    
    console.log('📊 当前页面结构:');
    console.log(JSON.stringify(currentSchema, null, 2));
    
    // 2. 创建正确的页面结构
    console.log('\n📋 2. 创建正确的页面结构');
    
    const timestamp = Date.now();
    const testBlockUid = `working-block-${timestamp}`;
    
    // 正确的页面结构应该是：
    const correctPageStructure = {
      type: 'void',
      'x-component': 'Page',
      'x-component-props': {},
      properties: {
        grid: {
          type: 'void',
          'x-component': 'Grid',
          'x-initializer': 'page:addBlock',
          properties: {
            [testBlockUid]: {
              type: 'void',
              name: testBlockUid,
              'x-uid': testBlockUid,
              'x-component': 'CardItem',
              'x-component-props': {
                title: `🎉 正确结构的测试区块 ${new Date().toLocaleTimeString()}`
              },
              properties: {
                content: {
                  type: 'void',
                  'x-component': 'Markdown.Void',
                  'x-component-props': {
                    content: `# 🚀 成功！页面结构已修复！

## 🎯 现在结构是正确的

这个区块现在位于正确的位置：
\`\`\`
page.schema.properties.grid.properties.${testBlockUid}
\`\`\`

### 📊 正确的页面结构

1. **Page** (页面根组件)
   - **Grid** (网格容器)
     - **CardItem** (区块容器)
       - **Markdown.Void** (内容组件)

### 🔍 技术细节

- **创建时间**: ${new Date().toLocaleString('zh-CN')}
- **区块 UID**: ${testBlockUid}
- **页面 UID**: ${pageUid}
- **Grid 初始化器**: page:addBlock

### 🎊 下一步

现在我们知道了正确的方法，可以：
1. 修复 MCP 工具中的区块添加逻辑
2. 确保区块总是添加到 Grid 容器中
3. 使用正确的 API 调用方式

---
*通过系统性调试和结构分析创建* ✨`
                  }
                }
              }
            }
          }
        }
      }
    };

    console.log('📊 正确的页面结构:');
    console.log(JSON.stringify(correctPageStructure, null, 2));

    // 3. 使用 patch 方法恢复正确结构
    console.log('\n📋 3. 恢复正确的页面结构');
    
    try {
      const patchResponse = await client.post('/uiSchemas:patch', {
        'x-uid': pageUid,
        schema: correctPageStructure
      });
      
      console.log('✅ 页面结构恢复成功!');
      console.log('📥 响应:', JSON.stringify(patchResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ 恢复页面结构失败:', error.response?.status, error.response?.data || error.message);
      return;
    }

    // 4. 验证恢复结果
    console.log('\n📋 4. 验证恢复结果');
    
    try {
      const verifyResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
      const verifiedSchema = verifyResponse.data.data;
      
      console.log('📊 恢复后的页面结构:');
      console.log(JSON.stringify(verifiedSchema, null, 2));
      
      // 检查结构是否正确
      const hasPage = verifiedSchema.schema?.['x-component'] === 'Page';
      const hasGrid = verifiedSchema.schema?.properties?.grid?.['x-component'] === 'Grid';
      const hasTestBlock = verifiedSchema.schema?.properties?.grid?.properties?.[testBlockUid];
      
      console.log('\n🔍 结构验证:');
      console.log(`   - 有 Page 组件: ${hasPage}`);
      console.log(`   - 有 Grid 组件: ${hasGrid}`);
      console.log(`   - 有测试区块: ${!!hasTestBlock}`);
      
      if (hasPage && hasGrid && hasTestBlock) {
        console.log('🎉 页面结构完全正确！');
        
        // 显示 Grid 中的内容
        const gridProperties = verifiedSchema.schema.properties.grid.properties;
        console.log('\n📋 Grid 中的区块:');
        Object.keys(gridProperties).forEach(key => {
          const block = gridProperties[key];
          console.log(`   - ${key}: ${block['x-component']} (${block['x-component-props']?.title || 'no title'})`);
        });
        
      } else {
        console.log('⚠️ 页面结构还有问题');
      }
      
    } catch (error) {
      console.log('❌ 验证失败:', error.response?.status, error.response?.data || error.message);
    }

    console.log('\n🎯 恢复完成！');
    console.log('\n📱 请刷新页面查看结果:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${pageUid}`);
    console.log('\n💡 如果看到区块，说明我们成功恢复了正确的页面结构！');
    
    console.log('\n🚀 现在我们可以：');
    console.log('   1. 修复 MCP 工具中的区块添加逻辑');
    console.log('   2. 确保区块总是添加到 Grid 容器中');
    console.log('   3. 使用正确的 API 调用方式');

  } catch (error) {
    console.error('❌ 恢复失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行恢复
restorePageStructure().catch(console.error);
