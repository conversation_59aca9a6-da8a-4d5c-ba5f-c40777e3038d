import axios from 'axios';
import https from 'https';
import testConfig from './test-config.js';

// 测试 MCP 各个功能
async function testMcpFunctions() {
  console.log('🧪 测试 MCP NocoBase 各个功能\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    },
    httpsAgent: new https.Agent({
      rejectUnauthorized: false
    })
  });

  try {
    // 1. 测试 Collections 功能
    console.log('📋 1. 测试 Collections 功能');
    
    // 1.1 列出当前 collections
    console.log('   1.1 列出当前 collections');
    const collectionsResponse = await client.get('/collections:list');
    const collections = collectionsResponse.data.data;
    console.log(`   ✅ 找到 ${collections.length} 个 collections:`);
    collections.forEach(collection => {
      console.log(`      - ${collection.name}: "${collection.title || 'N/A'}"`);
    });

    // 1.2 创建测试 collection
    console.log('\n   1.2 创建测试 collection');
    const testCollectionData = {
      name: 'test_products',
      title: 'Test Products',
      description: 'A test collection for products',
      fields: [
        {
          name: 'name',
          type: 'string',
          title: 'Product Name',
          required: true
        },
        {
          name: 'price',
          type: 'float',
          title: 'Price',
          required: true
        },
        {
          name: 'description',
          type: 'text',
          title: 'Description'
        }
      ]
    };

    try {
      const createResponse = await client.post('/collections:create', testCollectionData);
      console.log('   ✅ 创建测试 collection 成功: test_products');
    } catch (error) {
      console.log(`   ❌ 创建测试 collection 失败: ${error.response?.data?.message || error.message}`);
    }

    // 2. 测试 Records 功能
    console.log('\n📋 2. 测试 Records 功能');
    
    // 2.1 创建测试记录
    console.log('   2.1 创建测试记录');
    const testRecordData = {
      name: 'iPhone 15',
      price: 999.99,
      description: 'Latest iPhone model'
    };

    try {
      const recordResponse = await client.post('/test_products:create', testRecordData);
      console.log('   ✅ 创建测试记录成功');
      
      // 2.2 查询记录
      console.log('   2.2 查询记录');
      const listResponse = await client.get('/test_products:list');
      const records = listResponse.data.data;
      console.log(`   ✅ 找到 ${records.length} 条记录`);
      
    } catch (error) {
      console.log(`   ❌ 记录操作失败: ${error.response?.data?.message || error.message}`);
    }

    // 3. 测试 Users 功能
    console.log('\n📋 3. 测试 Users 功能');
    
    try {
      const usersResponse = await client.get('/users:list');
      const users = usersResponse.data.data;
      console.log(`   ✅ 找到 ${users.length} 个用户`);
      users.slice(0, 3).forEach(user => {
        console.log(`      - ID: ${user.id}, 用户名: ${user.username || user.nickname || 'N/A'}`);
      });
    } catch (error) {
      console.log(`   ❌ 获取用户列表失败: ${error.response?.data?.message || error.message}`);
    }

    // 4. 测试 Roles 功能
    console.log('\n📋 4. 测试 Roles 功能');
    
    try {
      const rolesResponse = await client.get('/roles:list');
      const roles = rolesResponse.data.data;
      console.log(`   ✅ 找到 ${roles.length} 个角色`);
      roles.forEach(role => {
        console.log(`      - 名称: ${role.name}, 标题: ${role.title || 'N/A'}`);
      });
    } catch (error) {
      console.log(`   ❌ 获取角色列表失败: ${error.response?.data?.message || error.message}`);
    }

    // 5. 测试 Routes 功能
    console.log('\n📋 5. 测试 Routes 功能');
    
    try {
      const routesResponse = await client.get('/desktopRoutes:list');
      const routes = routesResponse.data.data;
      console.log(`   ✅ 找到 ${routes.length} 个路由`);
      routes.forEach(route => {
        console.log(`      - ID: ${route.id}, 标题: "${route.title}", Schema UID: ${route.schemaUid}`);
      });
    } catch (error) {
      console.log(`   ❌ 获取路由列表失败: ${error.response?.data?.message || error.message}`);
    }

    // 6. 测试 Schema 功能
    console.log('\n📋 6. 测试 Schema 功能');
    
    try {
      const schemasResponse = await client.get('/uiSchemas:list?pageSize=5');
      const schemas = schemasResponse.data.data;
      console.log(`   ✅ 找到 ${schemas.length} 个 UI Schema (显示前5个)`);
      schemas.forEach(schema => {
        console.log(`      - UID: ${schema['x-uid']}, 组件: ${schema['x-component'] || 'N/A'}`);
      });
    } catch (error) {
      console.log(`   ❌ 获取 Schema 列表失败: ${error.response?.data?.message || error.message}`);
    }

    console.log('\n🎯 功能测试完成！');
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin`);
    console.log('\n💡 测试结果总结:');
    console.log('   - Collections: 已清理并可以创建新的');
    console.log('   - Records: 可以创建和查询');
    console.log('   - Users: 可以查询用户列表');
    console.log('   - Roles: 可以查询角色列表');
    console.log('   - Routes: 可以查询路由列表');
    console.log('   - Schemas: 可以查询 UI Schema');
    console.log('\n🚀 MCP 服务器功能正常！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
testMcpFunctions().catch(console.error);
