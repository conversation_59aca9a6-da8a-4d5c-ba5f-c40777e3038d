#!/usr/bin/env node

// 快速测试新页面
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let step = 0;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          step = 1;
          console.log('获取路由树...');
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_tree_overview', arguments: { includeIds: true } } });
        } else if (msg.id === 2 && step === 1) {
          console.log('=== 当前路由树 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'quick-test', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
