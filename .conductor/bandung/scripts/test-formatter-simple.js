#!/usr/bin/env node

/**
 * 简化的响应格式化器测试
 */

import { ResponseFormatter } from '../dist/utils/response-formatter.js';

// 创建大量数据来触发压缩
function createLargeData() {
  const data = {
    "_isJSONSchemaObject": true,
    "version": "2.0",
    "type": "void",
    "x-component": "Page",
    "x-uid": "cafh7yoyd6w",
    "x-async": false,
    "x-app-version": "1.7.10",
    "properties": {}
  };

  // 创建大量重复的属性来增加数据量
  for (let i = 0; i < 100; i++) {
    const key = Math.random().toString(36).substr(2, 11);
    data.properties[key] = {
      "_isJSONSchemaObject": true,
      "version": "2.0",
      "type": "void",
      "x-component": "Grid",
      "x-initializer": "page:addBlock",
      "x-uid": Math.random().toString(36).substr(2, 11),
      "x-async": false,
      "x-app-version": "1.7.10",
      "properties": {
        [Math.random().toString(36).substr(2, 11)]: {
          "_isJSONSchemaObject": true,
          "version": "2.0",
          "type": "void",
          "x-component": "Grid.Row",
          "x-uid": Math.random().toString(36).substr(2, 11),
          "x-async": false,
          "x-app-version": "1.7.10",
          "properties": {
            [Math.random().toString(36).substr(2, 11)]: {
              "_isJSONSchemaObject": true,
              "version": "2.0",
              "type": "void",
              "x-component": "Grid.Col",
              "x-uid": Math.random().toString(36).substr(2, 11),
              "x-async": false,
              "x-app-version": "1.7.10",
              "properties": {
                "table_block": {
                  "_isJSONSchemaObject": true,
                  "version": "2.0",
                  "type": "void",
                  "x-decorator": "TableBlockProvider",
                  "x-decorator-props": {
                    "collection": "users",
                    "dataSource": "main",
                    "action": "list",
                    "params": {
                      "pageSize": 20
                    },
                    "showIndex": true,
                    "dragSort": false
                  },
                  "x-component": "CardItem",
                  "x-uid": "table_users_main",
                  "x-async": false,
                  "x-app-version": "1.7.10"
                }
              }
            }
          }
        }
      }
    };
  }

  return data;
}

async function testFormatter() {
  console.log('🧪 测试响应格式化器 - 大数据压缩功能\n');
  console.log('=' .repeat(80));

  const formatter = new ResponseFormatter();
  const largeData = createLargeData();

  console.log('\n📋 原始数据大小:', JSON.stringify(largeData).length, '字符');
  console.log('📋 原始数据大小:', (JSON.stringify(largeData).length / 1024).toFixed(1), 'KB');

  console.log('\n🔄 开始格式化...\n');

  // 测试 Schema 数据格式化
  const result = formatter.formatResponse(largeData, 'schema');
  
  console.log('✅ 格式化结果:');
  console.log('-'.repeat(50));
  console.log(result);

  console.log('\n📊 压缩效果:');
  console.log(`原始大小: ${(JSON.stringify(largeData).length / 1024).toFixed(1)}KB`);
  console.log(`压缩后大小: ${(result.length / 1024).toFixed(1)}KB`);
  console.log(`压缩比: ${((1 - result.length / JSON.stringify(largeData).length) * 100).toFixed(1)}%`);
}

testFormatter().catch(console.error);
