#!/usr/bin/env node

import { NocoBaseClient } from '../dist/client.js';

const config = {
  baseUrl: 'http://103.121.94.113:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function logTest(name, success, error = null) {
  const status = success ? '✅' : '❌';
  console.log(`${status} ${name}`);
  if (error) {
    console.log(`   Error: ${error}`);
  }
}

async function testCollectionsAPI() {
  console.log('🧪 Testing Collections API Extensions...\n');
  
  try {
    const client = new NocoBaseClient(config);
    
    // 测试连接
    try {
      await client.listCollections();
      logTest('NocoBase connection', true);
    } catch (error) {
      logTest('NocoBase connection', false, error.message);
      return;
    }
    
    // 测试新的 Collections API
    console.log('\n📋 Testing Collections API...');
    
    // 1. 测试 listCollections (已存在)
    try {
      const collections = await client.listCollections();
      logTest('List collections', true);
      console.log(`   Found ${collections.length} collections`);
    } catch (error) {
      logTest('List collections', false, error.message);
    }
    
    // 2. 测试 listCollectionsMeta (已存在)
    try {
      const collections = await client.listCollectionsMeta();
      logTest('List collections with metadata', true);
      console.log(`   Found ${collections.length} collections with metadata`);
    } catch (error) {
      logTest('List collections with metadata', false, error.message);
    }
    
    // 测试新的 Fields API
    console.log('\n🔧 Testing Fields API...');
    
    // 获取第一个集合来测试 fields API
    let testCollectionName = null;
    try {
      const collections = await client.listCollections();
      if (collections.length > 0) {
        testCollectionName = collections[0].name;
        console.log(`   Using collection '${testCollectionName}' for fields testing`);
      }
    } catch (error) {
      console.log('   Could not get test collection');
    }
    
    if (testCollectionName) {
      // 3. 测试 listFields
      try {
        const fields = await client.listFields(testCollectionName);
        logTest('List fields', true);
        console.log(`   Found ${fields.length} fields in '${testCollectionName}'`);
      } catch (error) {
        logTest('List fields', false, error.message);
      }
      
      // 4. 测试 getField (如果有字段的话)
      try {
        const fields = await client.listFields(testCollectionName);
        if (fields.length > 0) {
          const firstField = fields[0];
          const field = await client.getField(testCollectionName, firstField.name);
          logTest('Get field', true);
          console.log(`   Retrieved field '${field.name}' of type '${field.type}'`);
        } else {
          console.log('   ⚠️  No fields found to test getField');
        }
      } catch (error) {
        logTest('Get field', false, error.message);
      }
    }
    
    // 测试创建和删除字段（谨慎测试）
    console.log('\n🛠️  Testing Field Creation/Deletion (if safe)...');
    
    if (testCollectionName) {
      // 创建一个测试字段
      try {
        const testField = {
          name: 'mcp_test_field',
          type: 'string',
          interface: 'input',
          description: 'Test field created by MCP server'
        };
        
        const createdField = await client.createField(testCollectionName, testField);
        logTest('Create field', true);
        console.log(`   Created field '${createdField.name}'`);
        
        // 立即删除测试字段
        try {
          await client.deleteField(testCollectionName, 'mcp_test_field');
          logTest('Delete field', true);
          console.log(`   Deleted test field 'mcp_test_field'`);
        } catch (error) {
          logTest('Delete field', false, error.message);
        }
        
      } catch (error) {
        logTest('Create field', false, error.message);
      }
    }
    
    console.log('\n✨ Collections API testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// 运行测试
testCollectionsAPI().catch(console.error);
