import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// MCP 服务器配置
const MCP_CONFIG = {
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
};

// 调用 MCP 工具的函数
async function callMcpTool(toolName, args = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 调用 MCP 工具: ${toolName}`);
    if (Object.keys(args).length > 0) {
      console.log(`📋 参数:`, JSON.stringify(args, null, 2));
    }

    const mcpRequest = {
      jsonrpc: "2.0",
      id: Date.now(),
      method: "tools/call",
      params: {
        name: toolName,
        arguments: args
      }
    };

    const mcpProcess = spawn('node', [
      join(__dirname, '../dist/index.js'),
      '--base-url', MCP_CONFIG.baseUrl,
      '--token', MCP_CONFIG.token,
      '--app', MCP_CONFIG.app
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    mcpProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    mcpProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    mcpProcess.on('close', (code) => {
      if (code === 0) {
        try {
          // 解析 MCP 响应
          const lines = stdout.trim().split('\n');
          const lastLine = lines[lines.length - 1];
          const response = JSON.parse(lastLine);
          
          console.log(`✅ 工具调用成功`);
          if (response.result && response.result.content) {
            response.result.content.forEach(content => {
              if (content.type === 'text') {
                console.log(content.text);
              }
            });
          }
          resolve(response);
        } catch (error) {
          console.log(`❌ 解析响应失败:`, error.message);
          console.log(`原始输出:`, stdout);
          reject(error);
        }
      } else {
        console.log(`❌ 工具调用失败 (退出码: ${code})`);
        console.log(`错误输出:`, stderr);
        reject(new Error(`MCP tool failed with code ${code}`));
      }
    });

    // 发送请求
    mcpProcess.stdin.write(JSON.stringify(mcpRequest) + '\n');
    mcpProcess.stdin.end();
  });
}

// 测试高级 MCP 区块功能
async function testAdvancedMcpBlocks() {
  console.log('🚀 测试 NocoBase MCP 高级区块功能\n');
  console.log('=' .repeat(60));

  try {
    // 1. 创建一个新的页面用于测试区块
    console.log('\n📋 1. 创建测试页面');
    const pageResult = await callMcpTool('create_page_route', {
      title: 'MCP Blocks Demo',
      template: 'blank',
      enableTabs: true,
      hidden: false
    });

    // 从响应中提取页面 UID
    let pageSchemaUid = null;
    if (pageResult.result && pageResult.result.content) {
      const content = pageResult.result.content[0].text;
      const match = content.match(/Page UID: ([a-zA-Z0-9-]+)/);
      if (match) {
        pageSchemaUid = match[1];
        console.log(`📌 页面 Schema UID: ${pageSchemaUid}`);
      }
    }

    if (!pageSchemaUid) {
      console.log('❌ 无法获取页面 Schema UID，跳过区块测试');
      return;
    }

    // 2. 为页面添加表格区块
    console.log('\n📋 2. 添加表格区块');
    await callMcpTool('add_table_block', {
      pageSchemaUid: pageSchemaUid,
      collectionName: 'mcp_test_items',
      title: 'MCP Test Items Table',
      showIndex: true,
      pageSize: 10
    });

    // 3. 添加详情区块
    console.log('\n📋 3. 添加详情区块');
    await callMcpTool('add_details_block', {
      pageSchemaUid: pageSchemaUid,
      collectionName: 'mcp_test_items',
      title: 'Item Details'
    });

    // 4. 添加列表区块
    console.log('\n📋 4. 添加列表区块');
    await callMcpTool('add_list_block', {
      pageSchemaUid: pageSchemaUid,
      collectionName: 'mcp_test_items',
      title: 'Items List',
      pageSize: 5
    });

    // 5. 添加 Markdown 区块
    console.log('\n📋 5. 添加 Markdown 区块');
    await callMcpTool('add_markdown_block', {
      pageSchemaUid: pageSchemaUid,
      title: 'Welcome',
      content: `# MCP Test Items Dashboard

This page was created using **NocoBase MCP Tools**!

## Features
- ✅ Table Block for data display
- ✅ Details Block for record details
- ✅ List Block for compact view
- ✅ Markdown Block for documentation

## Usage
Use the blocks above to manage your test items created via MCP tools.`
    });

    // 6. 测试表格操作工具
    console.log('\n📋 6. 测试表格操作工具');
    
    // 获取表格区块的 UID（这需要查询页面 schema）
    console.log('   6.1 查询页面 schema 以获取表格区块 UID');
    const schemaResult = await callMcpTool('get_page_schema', {
      schemaUid: pageSchemaUid
    });

    // 7. 添加表格列
    console.log('\n📋 7. 为表格添加列');
    try {
      await callMcpTool('add_table_column', {
        pageSchemaUid: pageSchemaUid,
        collectionName: 'mcp_test_items',
        fieldName: 'title',
        title: 'Title'
      });

      await callMcpTool('add_table_column', {
        pageSchemaUid: pageSchemaUid,
        collectionName: 'mcp_test_items',
        fieldName: 'description',
        title: 'Description'
      });
    } catch (error) {
      console.log('⚠️ 添加表格列可能需要特定的表格区块 UID');
    }

    // 8. 测试表格行操作
    console.log('\n📋 8. 添加表格行操作');
    try {
      await callMcpTool('add_table_row_action', {
        pageSchemaUid: pageSchemaUid,
        actionType: 'view',
        title: 'View Details'
      });

      await callMcpTool('add_table_row_action', {
        pageSchemaUid: pageSchemaUid,
        actionType: 'edit',
        title: 'Edit Item'
      });

      await callMcpTool('add_table_row_action', {
        pageSchemaUid: pageSchemaUid,
        actionType: 'delete',
        title: 'Delete Item'
      });
    } catch (error) {
      console.log('⚠️ 添加行操作可能需要特定的表格配置');
    }

    // 9. 测试筛选器
    console.log('\n📋 9. 添加筛选器');
    try {
      await callMcpTool('add_filter_block', {
        pageSchemaUid: pageSchemaUid,
        collectionName: 'mcp_test_items',
        title: 'Filter Items'
      });
    } catch (error) {
      console.log('⚠️ 添加筛选器可能需要特定的配置');
    }

    // 10. 验证创建的页面
    console.log('\n📋 10. 验证创建的页面');
    await callMcpTool('list_routes');

    console.log('\n🎯 高级区块测试完成！');
    console.log('=' .repeat(60));
    console.log('✅ 成功测试了以下高级功能:');
    console.log('   - 📄 页面创建 (带 Schema UID)');
    console.log('   - 📊 表格区块 (数据展示)');
    console.log('   - 📋 详情区块 (记录详情)');
    console.log('   - 📝 列表区块 (紧凑视图)');
    console.log('   - 📖 Markdown 区块 (文档说明)');
    console.log('   - 🔧 表格操作 (列、行操作)');
    console.log('   - 🔍 筛选器 (数据过滤)');
    
    console.log('\n📱 请在浏览器中验证新创建的页面:');
    console.log(`🔗 ${MCP_CONFIG.baseUrl.replace('/api', '')}/apps/${MCP_CONFIG.app}/admin`);
    console.log(`📌 页面名称: "MCP Blocks Demo"`);
    console.log(`📌 页面 Schema UID: ${pageSchemaUid}`);

  } catch (error) {
    console.error('❌ 高级区块测试失败:', error.message);
  }
}

// 运行测试
testAdvancedMcpBlocks().catch(console.error);
