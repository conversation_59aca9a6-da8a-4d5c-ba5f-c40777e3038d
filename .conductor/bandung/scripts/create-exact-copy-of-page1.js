#!/usr/bin/env node

// 创建一个完全复制 page_1 结构的页面
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let mainPageId = null;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        
        if (msg.id === 1 && msg.result) {
          console.log('步骤 1: 创建主页面（完全复制 page_1 结构）');
          // 使用 blank 模板，确保创建简单的 Page 组件
          send(server, { 
            jsonrpc: '2.0', 
            id: 2, 
            method: 'tools/call', 
            params: { 
              name: 'create_page_route', 
              arguments: { 
                title: `page_1 副本 ${Date.now()}`,
                template: 'blank',
                enableTabs: false, // 和 page_1 一样
                enableHeader: null,
                displayTitle: null
              } 
            } 
          });
        } 
        else if (msg.id === 2) {
          console.log('主页面创建结果:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 解析主页面 ID
          const text = content.find(item => item.type === 'text')?.text || '';
          const match = text.match(/"id":\s*(\d+)/);
          if (match) {
            mainPageId = parseInt(match[1]);
            console.log(`\n步骤 2: 为主页面 ${mainPageId} 创建 tabs 子路由`);
            
            // 创建 tabs 子路由，完全复制 page_1 的 tabs 结构
            send(server, {
              jsonrpc: '2.0',
              id: 3,
              method: 'tools/call',
              params: {
                name: 'create_tabs_route',
                arguments: {
                  parentId: mainPageId,
                  tabSchemaName: `tab_copy_${Date.now()}`
                }
              }
            });
          } else {
            console.log('无法解析主页面 ID');
            server.kill();
          }
        }
        else if (msg.id === 3) {
          console.log('\ntabs 子路由创建结果:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n步骤 3: 验证创建的页面结构');
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'routes_tree_overview', arguments: { includeIds: true } } });
        }
        else if (msg.id === 4) {
          console.log('\n=== 更新后的路由树 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n✅ 页面创建完成！');
          console.log('请测试新创建的页面是否能看到"添加区块"按钮');
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'create-exact-copy', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
