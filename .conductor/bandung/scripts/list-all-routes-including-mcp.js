#!/usr/bin/env node

// 列出所有路由，包括 MCP 创建的
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        
        if (msg.id === 1 && msg.result) {
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct_all', arguments: {} } });
        } 
        else if (msg.id === 2) {
          let raw = ''; for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          const payload = JSON.parse(raw); 
          const allRoutes = payload?.data || payload || [];
          
          console.log('=== 所有路由（包括隐藏的）===');
          
          function listAllRoutes(nodes = [], depth = 0) {
            for (const n of nodes) {
              const indent = '  '.repeat(depth);
              const hiddenMark = n.hidden ? ' (隐藏)' : '';
              const typeEmoji = n.type === 'page' ? '📄' : n.type === 'group' ? '📂' : n.type === 'tab' ? '🏷️' : n.type === 'tabs' ? '📑' : '❓';
              
              console.log(`${indent}${typeEmoji} ${n.title || '(无标题)'} [ID: ${n.id}, type: ${n.type}]${hiddenMark}`);
              console.log(`${indent}   schemaUid: ${n.schemaUid || 'N/A'}`);
              
              if (n.type === 'page') {
                console.log(`${indent}   enableTabs: ${n.enableTabs}`);
                console.log(`${indent}   enableHeader: ${n.enableHeader}`);
                console.log(`${indent}   displayTitle: ${n.displayTitle}`);
                console.log(`${indent}   options: ${JSON.stringify(n.options)}`);
              }
              
              if (n.tabSchemaName) {
                console.log(`${indent}   tabSchemaName: ${n.tabSchemaName}`);
              }
              
              console.log('');
              
              if (Array.isArray(n.children)) {
                listAllRoutes(n.children, depth + 1);
              }
            }
          }
          
          listAllRoutes(allRoutes);
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'list-all-routes', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });
