import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// MCP 服务器配置
const MCP_CONFIG = {
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
};

// 调用 MCP 工具的函数
async function callMcpTool(toolName, args = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 调用 MCP 工具: ${toolName}`);
    console.log(`📋 参数:`, JSON.stringify(args, null, 2));

    const mcpRequest = {
      jsonrpc: "2.0",
      id: Date.now(),
      method: "tools/call",
      params: {
        name: toolName,
        arguments: args
      }
    };

    const mcpProcess = spawn('node', [
      join(__dirname, '../dist/index.js'),
      '--base-url', MCP_CONFIG.baseUrl,
      '--token', MCP_CONFIG.token,
      '--app', MCP_CONFIG.app
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    mcpProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    mcpProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    mcpProcess.on('close', (code) => {
      if (code === 0) {
        try {
          // 解析 MCP 响应
          const lines = stdout.trim().split('\n');
          const lastLine = lines[lines.length - 1];
          const response = JSON.parse(lastLine);
          
          console.log(`✅ 工具调用成功`);
          if (response.result && response.result.content) {
            response.result.content.forEach(content => {
              if (content.type === 'text') {
                console.log(content.text);
              }
            });
          }
          resolve(response);
        } catch (error) {
          console.log(`❌ 解析响应失败:`, error.message);
          console.log(`原始输出:`, stdout);
          reject(error);
        }
      } else {
        console.log(`❌ 工具调用失败 (退出码: ${code})`);
        console.log(`错误输出:`, stderr);
        reject(new Error(`MCP tool failed with code ${code}`));
      }
    });

    // 发送请求
    mcpProcess.stdin.write(JSON.stringify(mcpRequest) + '\n');
    mcpProcess.stdin.end();
  });
}

// 测试 MCP 工具功能
async function testMcpTools() {
  console.log('🚀 测试 NocoBase MCP 工具功能\n');
  console.log('=' .repeat(60));

  try {
    // 1. 测试列出所有路由
    console.log('\n📋 1. 列出当前所有路由');
    await callMcpTool('list_routes');

    // 2. 测试创建页面路由
    console.log('\n📋 2. 创建新的页面路由');
    await callMcpTool('create_page_route', {
      title: 'MCP Test Page',
      template: 'blank',
      enableTabs: false,
      hidden: false
    });

    // 3. 测试创建组路由
    console.log('\n📋 3. 创建组路由');
    await callMcpTool('create_group_route', {
      title: 'MCP Test Group',
      icon: 'AppstoreOutlined'
    });

    // 4. 测试列出 collections
    console.log('\n📋 4. 列出所有 collections');
    await callMcpTool('list_collections');

    // 5. 测试创建简单的 collection
    console.log('\n📋 5. 创建测试 collection');
    await callMcpTool('create_collection', {
      name: 'mcp_test_items',
      title: 'MCP Test Items',
      description: 'Test collection created via MCP tools'
    });

    // 6. 测试为 collection 添加字段
    console.log('\n📋 6. 为 collection 添加字段');
    await callMcpTool('add_field', {
      collectionName: 'mcp_test_items',
      name: 'title',
      type: 'string',
      title: 'Item Title',
      required: true
    });

    await callMcpTool('add_field', {
      collectionName: 'mcp_test_items',
      name: 'description',
      type: 'text',
      title: 'Description'
    });

    // 7. 测试创建记录
    console.log('\n📋 7. 创建测试记录');
    await callMcpTool('create_record', {
      collectionName: 'mcp_test_items',
      data: {
        title: 'First MCP Item',
        description: 'This item was created using MCP tools'
      }
    });

    await callMcpTool('create_record', {
      collectionName: 'mcp_test_items',
      data: {
        title: 'Second MCP Item',
        description: 'Another test item via MCP'
      }
    });

    // 8. 测试查询记录
    console.log('\n📋 8. 查询记录');
    await callMcpTool('list_records', {
      collectionName: 'mcp_test_items'
    });

    // 9. 测试创建表格区块（如果有相关工具）
    console.log('\n📋 9. 尝试创建表格区块');
    try {
      await callMcpTool('add_table_block', {
        pageSchemaUid: 'page-test', // 这需要一个有效的页面 UID
        collectionName: 'mcp_test_items',
        title: 'MCP Test Items Table'
      });
    } catch (error) {
      console.log('⚠️ 表格区块创建可能需要有效的页面 UID');
    }

    console.log('\n🎯 MCP 工具测试完成！');
    console.log('=' .repeat(60));
    console.log('✅ 成功测试了以下功能:');
    console.log('   - 路由管理 (列出、创建页面、创建组)');
    console.log('   - Collection 管理 (列出、创建、添加字段)');
    console.log('   - 记录管理 (创建、查询)');
    console.log('   - 区块操作 (尝试创建表格区块)');
    
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${MCP_CONFIG.baseUrl.replace('/api', '')}/apps/${MCP_CONFIG.app}/admin`);

  } catch (error) {
    console.error('❌ MCP 工具测试失败:', error.message);
  }
}

// 运行测试
testMcpTools().catch(console.error);
