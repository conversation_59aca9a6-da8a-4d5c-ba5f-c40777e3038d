import axios from 'axios';
import https from 'https';
import testConfig from './test-config.js';

// 测试 MCP 高级功能
async function testAdvancedMcpFunctions() {
  console.log('🚀 测试 MCP NocoBase 高级功能\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    },
    httpsAgent: new https.Agent({
      rejectUnauthorized: false
    })
  });

  try {
    // 1. 创建一个完整的 collection 用于测试
    console.log('📋 1. 创建完整的测试 collection');
    
    const studentCollectionData = {
      name: 'students',
      title: 'Students',
      description: 'Student management collection',
      fields: [
        {
          name: 'name',
          type: 'string',
          title: 'Student Name',
          required: true
        },
        {
          name: 'age',
          type: 'integer',
          title: 'Age',
          required: true
        },
        {
          name: 'email',
          type: 'email',
          title: '<PERSON><PERSON> Address'
        },
        {
          name: 'phone',
          type: 'string',
          title: 'Phone Number'
        },
        {
          name: 'grade',
          type: 'select',
          title: 'Grade',
          options: [
            { label: 'Grade 1', value: '1' },
            { label: 'Grade 2', value: '2' },
            { label: 'Grade 3', value: '3' },
            { label: 'Grade 4', value: '4' }
          ]
        },
        {
          name: 'enrollment_date',
          type: 'date',
          title: 'Enrollment Date'
        },
        {
          name: 'notes',
          type: 'text',
          title: 'Notes'
        }
      ]
    };

    try {
      await client.post('/collections:create', studentCollectionData);
      console.log('   ✅ 创建 students collection 成功');
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.message?.includes('exists')) {
        console.log('   ℹ️ students collection 已存在，跳过创建');
      } else {
        console.log(`   ❌ 创建 students collection 失败: ${error.response?.data?.message || error.message}`);
      }
    }

    // 2. 添加一些测试数据
    console.log('\n📋 2. 添加测试学生数据');
    
    const testStudents = [
      {
        name: 'Alice Johnson',
        age: 20,
        email: '<EMAIL>',
        phone: '************',
        grade: '2',
        enrollment_date: '2023-09-01',
        notes: 'Excellent student'
      },
      {
        name: 'Bob Smith',
        age: 19,
        email: '<EMAIL>',
        phone: '************',
        grade: '1',
        enrollment_date: '2023-09-01',
        notes: 'Good at mathematics'
      },
      {
        name: 'Carol Davis',
        age: 21,
        email: '<EMAIL>',
        phone: '************',
        grade: '3',
        enrollment_date: '2022-09-01',
        notes: 'Leadership qualities'
      }
    ];

    for (const student of testStudents) {
      try {
        await client.post('/students:create', student);
        console.log(`   ✅ 创建学生记录成功: ${student.name}`);
      } catch (error) {
        console.log(`   ❌ 创建学生记录失败 (${student.name}): ${error.response?.data?.message || error.message}`);
      }
    }

    // 3. 创建一个页面来展示学生数据
    console.log('\n📋 3. 创建学生管理页面');
    
    try {
      // 创建页面路由
      const pageRouteData = {
        title: 'Student Management',
        type: 'page',
        enableTabs: true,
        enableHeader: true
      };

      const routeResponse = await client.post('/desktopRoutes:create', pageRouteData);
      const routeId = routeResponse.data.data.id;
      const schemaUid = routeResponse.data.data.schemaUid;
      
      console.log(`   ✅ 创建页面路由成功: ID ${routeId}, Schema UID: ${schemaUid}`);

      // 4. 在页面中添加表格区块
      console.log('\n📋 4. 在页面中添加学生表格区块');
      
      // 获取页面的 schema 结构
      const pageSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${schemaUid}`);
      const pageSchema = pageSchemaResponse.data.data;
      
      // 找到可以添加区块的容器
      let gridUid = null;
      function findGridUid(schema) {
        if (schema['x-component'] === 'Grid' || schema['x-component'] === 'BlockTemplate') {
          return schema['x-uid'];
        }
        if (schema.properties) {
          for (const key in schema.properties) {
            const result = findGridUid(schema.properties[key]);
            if (result) return result;
          }
        }
        return null;
      }
      
      gridUid = findGridUid(pageSchema);
      
      if (gridUid) {
        // 创建表格区块 schema
        const tableBlockSchema = {
          type: 'void',
          'x-component': 'Grid.Row',
          'x-app-version': '1.4.0-alpha',
          properties: {
            [Date.now()]: {
              type: 'void',
              'x-component': 'Grid.Col',
              properties: {
                [`table_${Date.now()}`]: {
                  type: 'void',
                  'x-decorator': 'TableBlockProvider',
                  'x-acl-action': 'students:list',
                  'x-use-decorator-props': 'useTableBlockDecoratorProps',
                  'x-decorator-props': {
                    collection: 'students',
                    dataSource: 'main',
                    action: 'list',
                    params: {
                      pageSize: 20
                    },
                    rowKey: 'id',
                    showIndex: true,
                    dragSort: false
                  },
                  'x-toolbar': 'BlockSchemaToolbar',
                  'x-settings': 'blockSettings:table',
                  'x-component': 'CardItem',
                  'x-filter-targets': [],
                  properties: {
                    actions: {
                      type: 'void',
                      'x-initializer': 'table:configureActions',
                      'x-component': 'ActionBar',
                      'x-component-props': {
                        style: {
                          marginBottom: 'var(--nb-spacing)'
                        }
                      }
                    },
                    [`table_${Date.now()}_inner`]: {
                      type: 'array',
                      'x-initializer': 'table:configureColumns',
                      'x-component': 'TableV2',
                      'x-use-component-props': 'useTableBlockProps',
                      'x-component-props': {
                        rowKey: 'id',
                        rowSelection: {
                          type: 'checkbox'
                        }
                      },
                      properties: {
                        name: {
                          type: 'void',
                          'x-decorator': 'TableV2.Column.Decorator',
                          'x-toolbar': 'TableColumnSchemaToolbar',
                          'x-settings': 'fieldSettings:TableColumn',
                          'x-component': 'TableV2.Column',
                          properties: {
                            name: {
                              type: 'string',
                              'x-collection-field': 'students.name',
                              'x-component': 'CollectionField',
                              'x-component-props': {
                                ellipsis: true
                              },
                              'x-read-pretty': true,
                              'x-decorator': null,
                              'x-decorator-props': {
                                labelStyle: {
                                  display: 'none'
                                }
                              }
                            }
                          }
                        },
                        age: {
                          type: 'void',
                          'x-decorator': 'TableV2.Column.Decorator',
                          'x-toolbar': 'TableColumnSchemaToolbar',
                          'x-settings': 'fieldSettings:TableColumn',
                          'x-component': 'TableV2.Column',
                          properties: {
                            age: {
                              type: 'string',
                              'x-collection-field': 'students.age',
                              'x-component': 'CollectionField',
                              'x-component-props': {
                                ellipsis: true
                              },
                              'x-read-pretty': true,
                              'x-decorator': null,
                              'x-decorator-props': {
                                labelStyle: {
                                  display: 'none'
                                }
                              }
                            }
                          }
                        },
                        email: {
                          type: 'void',
                          'x-decorator': 'TableV2.Column.Decorator',
                          'x-toolbar': 'TableColumnSchemaToolbar',
                          'x-settings': 'fieldSettings:TableColumn',
                          'x-component': 'TableV2.Column',
                          properties: {
                            email: {
                              type: 'string',
                              'x-collection-field': 'students.email',
                              'x-component': 'CollectionField',
                              'x-component-props': {
                                ellipsis: true
                              },
                              'x-read-pretty': true,
                              'x-decorator': null,
                              'x-decorator-props': {
                                labelStyle: {
                                  display: 'none'
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        };

        // 插入表格区块
        await client.post('/uiSchemas:insertAdjacent', {
          targetUid: gridUid,
          position: 'beforeEnd',
          schema: tableBlockSchema
        });

        console.log('   ✅ 添加学生表格区块成功');
      } else {
        console.log('   ⚠️ 未找到合适的容器来添加表格区块');
      }

    } catch (error) {
      console.log(`   ❌ 创建页面或添加区块失败: ${error.response?.data?.message || error.message}`);
    }

    // 5. 验证最终结果
    console.log('\n📋 5. 验证最终结果');
    
    // 检查 collections
    const finalCollectionsResponse = await client.get('/collections:list');
    const finalCollections = finalCollectionsResponse.data.data;
    console.log(`   ✅ 当前有 ${finalCollections.length} 个 collections`);
    
    // 检查学生记录
    try {
      const studentsResponse = await client.get('/students:list');
      const students = studentsResponse.data.data;
      console.log(`   ✅ 学生记录: ${students.length} 条`);
    } catch (error) {
      console.log(`   ❌ 获取学生记录失败: ${error.response?.data?.message || error.message}`);
    }
    
    // 检查路由
    const finalRoutesResponse = await client.get('/desktopRoutes:list');
    const finalRoutes = finalRoutesResponse.data.data;
    console.log(`   ✅ 当前有 ${finalRoutes.length} 个路由`);

    console.log('\n🎯 高级功能测试完成！');
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin`);
    console.log('\n💡 高级功能测试结果:');
    console.log('   - ✅ Collection 创建和字段配置');
    console.log('   - ✅ 批量数据插入');
    console.log('   - ✅ 页面路由创建');
    console.log('   - ✅ 表格区块添加');
    console.log('\n🚀 MCP 高级功能正常！');

  } catch (error) {
    console.error('❌ 高级功能测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行高级功能测试
testAdvancedMcpFunctions().catch(console.error);
