## MCP Server for NocoBase

一个用于与 NocoBase 应用交互的 Model Context Protocol (MCP) 服务器，专注于集合（Collections）、记录（Records）、Schema 相关功能。

### 特性概览
- 集合管理：创建、读取、更新、删除（CRUD）
- 记录操作：分页查询、过滤、排序、关联展开、CRUD
- Schema 能力：集合字段与 UI Schema 检索、页面结构分析
- 集合分类管理：获取集合的分类信息
- 资源暴露：将 NocoBase 的集合、Schema 与记录通过 MCP Resources 暴露给客户端

---

## 技术栈
- 运行环境：Node.js >= 18
- 语言与构建：TypeScript、tsc
- 协议 SDK：@modelcontextprotocol/sdk
- HTTP 客户端：axios
- 参数校验：zod
- 质量保障：eslint、jest

---

## 目录结构
- src/
  - index.ts：MCP 服务器入口，注册所有工具与资源
  - client.ts：NocoBase REST API 客户端封装（Collections/Records/Schema）
  - tools/
    - collections.ts：集合相关工具
    - records.ts：记录相关工具
    - schema.ts：集合与页面 Schema 工具
    - collection-categories.ts：集合分类相关工具
  - resources/
    - index.ts：将集合、Schema、记录暴露为 MCP Resources
  - utils.ts：工具函数
- scripts/：用于本地调试与验证的脚本（API 连接、路由/区块演示等）
- docs/：项目文档、接口说明、进展报告
- dist/：构建产物

---

## 安装与开发

- 安装依赖
```
npm install
```

- 开发模式（直接以 ts 运行）
```
npm run dev
```

- 构建与启动
```
npm run build
npm start
```

- 代码质量与测试
```
npm run lint
npm test
```

---

## 使用方法

### 命令行启动与参数
构建后会提供 CLI：mcp-nocobase-collections（bin 指向 dist/index.js）。

- 命令行参数
  - --base-url <url>：NocoBase 实例 API 基地址（必填）
  - --token <token>：认证 Token（必填）
  - --app <name>：应用 App ID（必填）
  - --help：显示帮助

- 环境变量（作为参数回退）
  - NOCOBASE_BASE_URL
  - NOCOBASE_TOKEN
  - NOCOBASE_APP

示例：
```
mcp-nocobase-collections \
  --base-url https://your-nocobase.com/api \
  --token xxx \
  --app your_app
```

### 与 Claude Desktop 集成
将以下配置加入：~/Library/Application Support/Claude/claude_desktop_config.json
```
{
  "mcpServers": {
    "nocobase": {
      "command": "mcp-nocobase-collections",
      "args": [
        "--base-url", "https://your-nocobase-instance.com/api",
        "--token", "your-auth-token",
        "--app", "your-app-id"
      ]
    }
  }
}
```

---

## 配置说明
服务器可通过命令行参数或环境变量配置。更多背景与 API 约定可参考：
- docs/nocobase_api_intro.md
- docs/nocobase_collections_api_reference.md
- docs/nocobase_collections_api_quick_reference.md

---

## 可用工具（Tools）

### 集合管理
- list_collections(includeMeta?)：列出集合（可选元信息）
- get_collection(name)：获取集合详情
- create_collection(name, title?, description?, autoGenId?, createdAt?, updatedAt?, createdBy?, updatedBy?, fields?)
- update_collection(name, title?, description?, hidden?)
- delete_collection(name, confirm)

### 记录操作（传入 collection、id 或筛选）
- list_records(collection, page?, pageSize?, filter?, sort?, appends?)
- get_record(collection, id, appends?)
- create_record(collection, data)
- update_record(collection, id, data)
- delete_record(collection, id, confirm)

### 数据库 Schema（Collections）
- get_collection_schema(collection)：集合字段与配置
- list_fields(collection)：列出字段
- create_field(collection, name, type, interface?, description?, uiSchema?)

### UI Schema / 页面结构（前端）
- analyze_page_schema(schemaUid)：递归分析页面 UI Schema 结构（组件、装饰器、属性、ACL、数据源等）
- get_schema_properties(schemaUid)：获取指定节点的 properties（子区块）

### 集合分类管理
- get_collection_categories()：获取所有集合分类
- get_collections_by_category(categoryName)：获取指定分类下的集合


---

## 可用资源（Resources）
- collections://list：集合列表（JSON）
- collections://{name}：集合详情（JSON）
- collections://{name}/schema：集合 Schema（配置与字段）
- collections://{name}/records：集合记录（默认最多 50 条）
- collections://{name}/records/{id}：单条记录详情

---

## 开发进展与里程碑

### 当前功能
- 集合管理：完整的 CRUD 操作
- 记录操作：分页查询、过滤、排序、关联展开
- Schema 管理：集合字段和 UI Schema 检索
- 集合分类：获取集合的分类信息
- 资源暴露：通过 MCP Resources 暴露数据

### 相关文档
- docs/README.md：基础功能与使用说明

---

## 测试与验证

### 测试脚本
tests/nocobase/ 目录下提供了完整的测试套件：

#### MCP 工具测试
- `test-mcp-comprehensive.js` - 综合功能测试
- `test-mcp-tools-improved.js` - 基本工具调用测试
- `test-mcp-update-record.js` - 记录更新测试
- `test-mcp-delete-record.js` - 记录删除测试
- `test-mcp-delete-collection.js` - 集合删除测试

#### 客户端测试
- `view-collections-detail.js` - 查看集合详情
- `list-collections-simple.js` - 简单集合列表
- `test-collections-env.js` - 多环境测试

#### 调试工具
- `debug-mcp-output.js` - MCP 协议调试

### 运行测试
```bash
# 运行所有测试
./run-tests.sh

# 运行单个测试
node tests/nocobase/test-mcp-comprehensive.js

# 单元测试
npm test
```

在运行脚本前，请先 npm run build，脚本默认从 dist/ 引用客户端实现。

> 注：MCP 端到端集成测试可结合 Claude / LLM 客户端进行；安全起见，请勿在公开仓库中提交真实生产凭证。

---

## 许可证与贡献
- 许可证：MIT（见 LICENSE）
- 贡献：欢迎提交 PR / Issue 改进工具能力与文档

