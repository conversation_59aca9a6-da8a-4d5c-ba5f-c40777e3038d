#!/bin/bash

# 设置环境变量
export NOCOBASE_API_URL="http://app.dev.orb.local/api"
export NOCOBASE_APP_ID="mcp_playground"
export NOCOBASE_AUTH_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjIsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUzMDU3ODEsImV4cCI6MzMzMTI5MDU3ODF9.t84vq8s6_gW2MVzdBZ0m03x7n8o-aPOAu7RTLVjzVPs"

# 使用编译后的 JS 文件运行
echo "启动 NocoBase MCP Collections Server..."
echo "Base URL: https://n.astra.xin/apps/mcp_playground"
echo "App: mcp_playground"
echo ""

node dist/index.js --base-url https://n.astra.xin/apps/mcp_playground --token neo@123 --app mcp_playground